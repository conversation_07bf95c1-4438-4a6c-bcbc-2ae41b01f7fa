import 'dart:async';
import 'package:supabase_flutter/supabase_flutter.dart' hide User;
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';
import '../models/user.dart';
import 'supabase_service.dart';

enum AuthState {
  initial,
  authenticated,
  unauthenticated,
  loading,
}

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  final SupabaseService _supabaseService = SupabaseService();
  
  final StreamController<AuthState> _authStateController = 
      StreamController<AuthState>.broadcast();
  final StreamController<User?> _userController = 
      StreamController<User?>.broadcast();
  final StreamController<String?> _errorController = 
      StreamController<String?>.broadcast();

  AuthState _currentState = AuthState.initial;
  User? _currentUser;
  String? _lastError;

  // Getters
  AuthState get currentState => _currentState;
  User? get currentUser => _currentUser;
  String? get lastError => _lastError;
  bool get isAuthenticated => _currentState == AuthState.authenticated;

  // Streams
  Stream<AuthState> get authStateStream => _authStateController.stream;
  Stream<User?> get userStream => _userController.stream;
  Stream<String?> get errorStream => _errorController.stream;

  Future<void> initialize() async {
    _updateState(AuthState.loading);
    
    // Check if user is already signed in
    final session = _supabase.auth.currentSession;
    if (session != null && session.user != null) {
      await _loadUserProfile(session.user!.id);
      _updateState(AuthState.authenticated);
    } else {
      _updateState(AuthState.unauthenticated);
    }

    // Listen to auth state changes
    _supabase.auth.onAuthStateChange.listen((data) {
      _handleAuthStateChange(data.event, data.session);
    });
  }

  void _handleAuthStateChange(AuthChangeEvent event, Session? session) {
    switch (event) {
      case AuthChangeEvent.signedIn:
        if (session?.user != null) {
          _loadUserProfile(session!.user!.id);
          _updateState(AuthState.authenticated);
        }
        break;
      case AuthChangeEvent.signedOut:
        _currentUser = null;
        _userController.add(null);
        _updateState(AuthState.unauthenticated);
        _clearLocalData();
        break;
      case AuthChangeEvent.tokenRefreshed:
        // Token refreshed, user is still authenticated
        break;
      default:
        break;
    }
  }

  Future<void> _loadUserProfile(String userId) async {
    try {
      // Try to get user from Supabase first
      final response = await _supabase
          .from('users')
          .select()
          .eq('id', userId)
          .maybeSingle();

      if (response != null) {
        _currentUser = User.fromJson(response);
      } else {
        // Create user profile if it doesn't exist
        final supabaseUser = _supabase.auth.currentUser!;
        _currentUser = User(
          id: userId,
          email: supabaseUser.email,
          name: supabaseUser.userMetadata?['name'] as String? ?? 
                supabaseUser.email?.split('@').first ?? 'User',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Save to Supabase
        await _supabase.from('users').insert(_currentUser!.toJson());
      }

      _userController.add(_currentUser);
      await _saveUserToPreferences(_currentUser!);
    } catch (e) {
      _setError('Failed to load user profile: $e');
    }
  }

  Future<bool> signInWithEmail(String email, String password) async {
    try {
      _updateState(AuthState.loading);
      _clearError();

      final response = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user != null) {
        await _loadUserProfile(response.user!.id);
        _updateState(AuthState.authenticated);
        return true;
      } else {
        _setError('Sign in failed');
        _updateState(AuthState.unauthenticated);
        return false;
      }
    } catch (e) {
      _setError('Sign in error: $e');
      _updateState(AuthState.unauthenticated);
      return false;
    }
  }

  Future<bool> signUpWithEmail(String email, String password, String name) async {
    try {
      _updateState(AuthState.loading);
      _clearError();

      final response = await _supabase.auth.signUp(
        email: email,
        password: password,
        data: {'name': name},
      );

      if (response.user != null) {
        // Create user profile
        final user = User(
          id: response.user!.id,
          email: email,
          name: name,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Save to Supabase
        await _supabase.from('users').insert(user.toJson());
        
        _currentUser = user;
        _userController.add(_currentUser);
        await _saveUserToPreferences(user);
        
        _updateState(AuthState.authenticated);
        return true;
      } else {
        _setError('Sign up failed');
        _updateState(AuthState.unauthenticated);
        return false;
      }
    } catch (e) {
      _setError('Sign up error: $e');
      _updateState(AuthState.unauthenticated);
      return false;
    }
  }

  Future<bool> signInWithGoogle() async {
    try {
      _updateState(AuthState.loading);
      _clearError();

      final response = await _supabase.auth.signInWithOAuth(
        Provider.google,
        redirectTo: AppConstants.redirectUrl,
      );

      // OAuth sign-in is handled by the auth state change listener
      return response;
    } catch (e) {
      _setError('Google sign in error: $e');
      _updateState(AuthState.unauthenticated);
      return false;
    }
  }

  Future<bool> resetPassword(String email) async {
    try {
      _clearError();

      await _supabase.auth.resetPasswordForEmail(
        email,
        redirectTo: AppConstants.resetPasswordUrl,
      );

      return true;
    } catch (e) {
      _setError('Password reset error: $e');
      return false;
    }
  }

  Future<bool> updatePassword(String newPassword) async {
    try {
      _clearError();

      final response = await _supabase.auth.updateUser(
        UserAttributes(password: newPassword),
      );

      return response.user != null;
    } catch (e) {
      _setError('Password update error: $e');
      return false;
    }
  }

  Future<bool> updateProfile({
    String? name,
    String? email,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      _clearError();

      if (_currentUser == null) {
        _setError('No user signed in');
        return false;
      }

      // Update Supabase auth user if email is being changed
      if (email != null && email != _currentUser!.email) {
        await _supabase.auth.updateUser(
          UserAttributes(email: email),
        );
      }

      // Update user profile in database
      final updatedUser = _currentUser!.copyWith(
        name: name ?? _currentUser!.name,
        email: email ?? _currentUser!.email,
        updatedAt: DateTime.now(),
      );

      await _supabase.from('users').update(updatedUser.toJson()).eq('id', updatedUser.id);

      _currentUser = updatedUser;
      _userController.add(_currentUser);
      await _saveUserToPreferences(updatedUser);

      return true;
    } catch (e) {
      _setError('Profile update error: $e');
      return false;
    }
  }

  Future<void> signOut() async {
    try {
      _updateState(AuthState.loading);
      await _supabase.auth.signOut();
      // Auth state change listener will handle the rest
    } catch (e) {
      _setError('Sign out error: $e');
      _updateState(AuthState.unauthenticated);
    }
  }

  Future<void> _saveUserToPreferences(User user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.keyUserId, user.id);
    await prefs.setString(AppConstants.keyUserEmail, user.email ?? '');
    await prefs.setString(AppConstants.keyUserName, user.name ?? '');
  }

  Future<void> _clearLocalData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.keyUserId);
    await prefs.remove(AppConstants.keyUserEmail);
    await prefs.remove(AppConstants.keyUserName);
    await prefs.remove(AppConstants.keyLastSyncTime);
  }

  void _updateState(AuthState state) {
    _currentState = state;
    _authStateController.add(state);
  }

  void _setError(String error) {
    _lastError = error;
    _errorController.add(error);
  }

  void _clearError() {
    _lastError = null;
    _errorController.add(null);
  }

  void dispose() {
    _authStateController.close();
    _userController.close();
    _errorController.close();
  }
}
