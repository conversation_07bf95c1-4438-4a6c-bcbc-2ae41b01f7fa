import 'package:flutter/material.dart';
import 'dart:io';
import '../../../../core/models/user.dart';
import '../../../../core/constants/app_constants.dart';

class ProfileAvatar extends StatelessWidget {
  final User user;
  final File? selectedImage;
  final bool isEditing;
  final VoidCallback? onImageSelected;

  const ProfileAvatar({
    super.key,
    required this.user,
    this.selectedImage,
    this.isEditing = false,
    this.onImageSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Stack(
        children: [
          CircleAvatar(
            radius: 60,
            backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
            backgroundImage: selectedImage != null ? FileImage(selectedImage!) : null,
            child: selectedImage == null && user.avatarUrl == null
                ? Text(
                    _getInitials(user.name ?? user.email ?? 'U'),
                    style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                : null,
          ),
          if (isEditing)
            Positioned(
              bottom: 0,
              right: 0,
              child: Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  icon: Icon(
                    Icons.camera_alt,
                    color: Theme.of(context).colorScheme.onPrimary,
                    size: 20,
                  ),
                  onPressed: onImageSelected,
                ),
              ),
            ),
        ],
      ),
    );
  }

  String _getInitials(String name) {
    final words = name.trim().split(' ');
    if (words.length >= 2) {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    } else if (words.isNotEmpty) {
      return words[0][0].toUpperCase();
    }
    return 'U';
  }
}
