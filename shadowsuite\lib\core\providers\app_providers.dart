import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';

// Theme Mode Provider
final themeModeProvider = StateNotifierProvider<ThemeModeNotifier, ThemeMode>(
  (ref) => ThemeModeNotifier(),
);

class ThemeModeNotifier extends StateNotifier<ThemeMode> {
  ThemeModeNotifier() : super(ThemeMode.system) {
    _loadThemeMode();
  }

  Future<void> _loadThemeMode() async {
    final prefs = await SharedPreferences.getInstance();
    final themeModeString = prefs.getString(AppConstants.keyThemeMode);
    if (themeModeString != null) {
      state = ThemeMode.values.firstWhere(
        (mode) => mode.toString() == themeModeString,
        orElse: () => ThemeMode.system,
      );
    }
  }

  Future<void> setThemeMode(ThemeMode themeMode) async {
    state = themeMode;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.keyThemeMode, themeMode.toString());
  }
}

// Locale Provider
final localeProvider = StateNotifierProvider<LocaleNotifier, Locale?>(
  (ref) => LocaleNotifier(),
);

class LocaleNotifier extends StateNotifier<Locale?> {
  LocaleNotifier() : super(null) {
    _loadLocale();
  }

  Future<void> _loadLocale() async {
    final prefs = await SharedPreferences.getInstance();
    final languageCode = prefs.getString(AppConstants.keyLanguage);
    if (languageCode != null) {
      state = Locale(languageCode);
    }
  }

  Future<void> setLocale(Locale locale) async {
    state = locale;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.keyLanguage, locale.languageCode);
  }
}

// User Provider
final userProvider = StateNotifierProvider<UserNotifier, AsyncValue<String?>>(
  (ref) => UserNotifier(),
);

class UserNotifier extends StateNotifier<AsyncValue<String?>> {
  UserNotifier() : super(const AsyncValue.loading()) {
    _loadUser();
  }

  Future<void> _loadUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getString(AppConstants.keyUserId);
      state = AsyncValue.data(userId);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> setUser(String? userId) async {
    try {
      state = const AsyncValue.loading();
      final prefs = await SharedPreferences.getInstance();
      if (userId != null) {
        await prefs.setString(AppConstants.keyUserId, userId);
      } else {
        await prefs.remove(AppConstants.keyUserId);
      }
      state = AsyncValue.data(userId);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> signOut() async {
    await setUser(null);
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.keyUserEmail);
    await prefs.remove(AppConstants.keyLastSyncTime);
  }
}

// Connectivity Provider
final connectivityProvider = StateNotifierProvider<ConnectivityNotifier, bool>(
  (ref) => ConnectivityNotifier(),
);

class ConnectivityNotifier extends StateNotifier<bool> {
  ConnectivityNotifier() : super(true) {
    _checkConnectivity();
  }

  Future<void> _checkConnectivity() async {
    // This will be implemented with connectivity_plus package
    // For now, assume we're connected
    state = true;
  }

  void setConnectivity(bool isConnected) {
    state = isConnected;
  }
}

// Sync Status Provider
final syncStatusProvider = StateNotifierProvider<SyncStatusNotifier, SyncStatus>(
  (ref) => SyncStatusNotifier(),
);

enum SyncStatus {
  idle,
  syncing,
  success,
  error,
}

class SyncStatusNotifier extends StateNotifier<SyncStatus> {
  SyncStatusNotifier() : super(SyncStatus.idle);

  void setSyncStatus(SyncStatus status) {
    state = status;
  }

  Future<void> startSync() async {
    state = SyncStatus.syncing;
    try {
      // Implement sync logic here
      await Future.delayed(const Duration(seconds: 2)); // Simulate sync
      state = SyncStatus.success;
      
      // Reset to idle after a delay
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          state = SyncStatus.idle;
        }
      });
    } catch (error) {
      state = SyncStatus.error;
      
      // Reset to idle after a delay
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          state = SyncStatus.idle;
        }
      });
    }
  }
}

// Navigation Provider
final navigationProvider = StateNotifierProvider<NavigationNotifier, int>(
  (ref) => NavigationNotifier(),
);

class NavigationNotifier extends StateNotifier<int> {
  NavigationNotifier() : super(0);

  void setIndex(int index) {
    state = index;
  }
}

// Loading Provider
final loadingProvider = StateNotifierProvider<LoadingNotifier, bool>(
  (ref) => LoadingNotifier(),
);

class LoadingNotifier extends StateNotifier<bool> {
  LoadingNotifier() : super(false);

  void setLoading(bool isLoading) {
    state = isLoading;
  }
}

// Error Provider
final errorProvider = StateNotifierProvider<ErrorNotifier, String?>(
  (ref) => ErrorNotifier(),
);

class ErrorNotifier extends StateNotifier<String?> {
  ErrorNotifier() : super(null);

  void setError(String? error) {
    state = error;
  }

  void clearError() {
    state = null;
  }
}

// First Launch Provider
final firstLaunchProvider = FutureProvider<bool>((ref) async {
  final prefs = await SharedPreferences.getInstance();
  return prefs.getBool(AppConstants.keyFirstLaunch) ?? true;
});

// Set First Launch Complete
final setFirstLaunchCompleteProvider = Provider<Future<void> Function()>((ref) {
  return () async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(AppConstants.keyFirstLaunch, false);
  };
});

// Offline Mode Provider
final offlineModeProvider = StateNotifierProvider<OfflineModeNotifier, bool>(
  (ref) => OfflineModeNotifier(),
);

class OfflineModeNotifier extends StateNotifier<bool> {
  OfflineModeNotifier() : super(false) {
    _loadOfflineMode();
  }

  Future<void> _loadOfflineMode() async {
    final prefs = await SharedPreferences.getInstance();
    state = prefs.getBool(AppConstants.keyOfflineMode) ?? false;
  }

  Future<void> setOfflineMode(bool isOffline) async {
    state = isOffline;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(AppConstants.keyOfflineMode, isOffline);
  }
}
