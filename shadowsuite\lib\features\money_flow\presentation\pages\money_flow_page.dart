import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_nav_bar/google_nav_bar.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../shared/theme/app_theme.dart';
import '../../../../l10n/app_localizations.dart';
import '../widgets/dashboard_tab.dart';
import '../widgets/accounts_tab.dart';
import '../widgets/categories_tab.dart';
import '../widgets/transactions_tab.dart';
import '../widgets/recurring_tab.dart';
import '../widgets/budgets_tab.dart';
import '../widgets/reports_tab.dart';

class MoneyFlowPage extends ConsumerStatefulWidget {
  const MoneyFlowPage({super.key});

  @override
  ConsumerState<MoneyFlowPage> createState() => _MoneyFlowPageState();
}

class _MoneyFlowPageState extends ConsumerState<MoneyFlowPage> {
  int _selectedIndex = 0;
  final PageController _pageController = PageController();

  final List<Widget> _pages = [
    const MoneyFlowDashboardTab(),
    const AccountsTab(),
    const CategoriesTab(),
    const TransactionsTab(),
    const RecurringTab(),
    const BudgetsTab(),
    const ReportsTab(),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.moneyFlow),
        centerTitle: true,
        backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              _showAddTransactionDialog();
            },
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // Navigate to search
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _selectedIndex = index;
                });
              },
              children: _pages,
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          boxShadow: [
            BoxShadow(blurRadius: 20, color: Colors.black.withOpacity(.1)),
          ],
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.defaultPadding,
              vertical: AppConstants.defaultMargin,
            ),
            child: GNav(
              rippleColor: AppTheme.primaryColor.withOpacity(0.1),
              hoverColor: AppTheme.primaryColor.withOpacity(0.05),
              gap: 8,
              activeColor: AppTheme.primaryColor,
              iconSize: 24,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              duration: const Duration(milliseconds: 400),
              tabBackgroundColor: AppTheme.primaryColor.withOpacity(0.1),
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              tabs: [
                GButton(icon: Icons.dashboard_outlined, text: l10n.dashboard),
                GButton(
                  icon: Icons.account_balance_outlined,
                  text: l10n.accounts,
                ),
                GButton(icon: Icons.category_outlined, text: l10n.categories),
                GButton(
                  icon: Icons.receipt_long_outlined,
                  text: l10n.transactions,
                ),
                GButton(icon: Icons.repeat_outlined, text: l10n.recurring),
                GButton(icon: Icons.savings_outlined, text: l10n.budgets),
                GButton(icon: Icons.analytics_outlined, text: l10n.reports),
              ],
              selectedIndex: _selectedIndex,
              onTabChange: _onItemTapped,
            ),
          ),
        ),
      ),
    );
  }

  void _showAddTransactionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Transaction'),
        content: const Text('Quick add transaction dialog would go here'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to add transaction page
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }
}
