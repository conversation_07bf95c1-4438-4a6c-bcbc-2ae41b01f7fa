import 'package:sqflite/sqflite.dart';
import '../database/database_helper.dart';
import '../models/user.dart';
import '../models/memo.dart';
import '../models/athkar_routine.dart';
import '../models/money_flow.dart' as models;

abstract class DatabaseService<T> {
  Future<String> insert(T item);
  Future<T?> getById(String id);
  Future<List<T>> getAll({String? userId});
  Future<void> update(T item);
  Future<void> delete(String id);
  Future<List<T>> search(String query, {String? userId});
}

class UserDatabaseService implements DatabaseService<User> {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  @override
  Future<String> insert(User user) async {
    final db = await _dbHelper.database;
    await db.insert('users', user.toMap());
    return user.id;
  }

  @override
  Future<User?> getById(String id) async {
    final db = await _dbHelper.database;
    final maps = await db.query('users', where: 'id = ?', whereArgs: [id]);

    if (maps.isNotEmpty) {
      return User.fromMap(maps.first);
    }
    return null;
  }

  @override
  Future<List<User>> getAll({String? userId}) async {
    final db = await _dbHelper.database;
    final maps = await db.query('users');
    return maps.map((map) => User.fromMap(map)).toList();
  }

  @override
  Future<void> update(User user) async {
    final db = await _dbHelper.database;
    await db.update(
      'users',
      user.toMap(),
      where: 'id = ?',
      whereArgs: [user.id],
    );
  }

  @override
  Future<void> delete(String id) async {
    final db = await _dbHelper.database;
    await db.delete('users', where: 'id = ?', whereArgs: [id]);
  }

  @override
  Future<List<User>> search(String query, {String? userId}) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'users',
      where: 'name LIKE ? OR email LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
    );
    return maps.map((map) => User.fromMap(map)).toList();
  }

  Future<User?> getByEmail(String email) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'users',
      where: 'email = ?',
      whereArgs: [email],
    );

    if (maps.isNotEmpty) {
      return User.fromMap(maps.first);
    }
    return null;
  }
}

class MemoDatabaseService implements DatabaseService<Memo> {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  @override
  Future<String> insert(Memo memo) async {
    final db = await _dbHelper.database;
    await db.insert('memos', memo.toMap());
    return memo.id;
  }

  @override
  Future<Memo?> getById(String id) async {
    final db = await _dbHelper.database;
    final maps = await db.query('memos', where: 'id = ?', whereArgs: [id]);

    if (maps.isNotEmpty) {
      return Memo.fromMap(maps.first);
    }
    return null;
  }

  @override
  Future<List<Memo>> getAll({String? userId}) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'memos',
      where: userId != null ? 'user_id = ?' : null,
      whereArgs: userId != null ? [userId] : null,
      orderBy: 'created_at DESC',
    );
    return maps.map((map) => Memo.fromMap(map)).toList();
  }

  @override
  Future<void> update(Memo memo) async {
    final db = await _dbHelper.database;
    await db.update(
      'memos',
      memo.toMap(),
      where: 'id = ?',
      whereArgs: [memo.id],
    );
  }

  @override
  Future<void> delete(String id) async {
    final db = await _dbHelper.database;
    await db.delete('memos', where: 'id = ?', whereArgs: [id]);
  }

  @override
  Future<List<Memo>> search(String query, {String? userId}) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'memos',
      where: userId != null
          ? 'user_id = ? AND (title LIKE ? OR description LIKE ? OR transcription LIKE ?)'
          : 'title LIKE ? OR description LIKE ? OR transcription LIKE ?',
      whereArgs: userId != null
          ? [userId, '%$query%', '%$query%', '%$query%']
          : ['%$query%', '%$query%', '%$query%'],
      orderBy: 'created_at DESC',
    );
    return maps.map((map) => Memo.fromMap(map)).toList();
  }

  Future<List<Memo>> getRecentMemos({String? userId, int limit = 10}) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'memos',
      where: userId != null ? 'user_id = ?' : null,
      whereArgs: userId != null ? [userId] : null,
      orderBy: 'created_at DESC',
      limit: limit,
    );
    return maps.map((map) => Memo.fromMap(map)).toList();
  }
}

class AthkarDatabaseService implements DatabaseService<AthkarRoutine> {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  @override
  Future<String> insert(AthkarRoutine routine) async {
    final db = await _dbHelper.database;
    await db.insert('athkar_routines', routine.toMap());
    return routine.id;
  }

  @override
  Future<AthkarRoutine?> getById(String id) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'athkar_routines',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return AthkarRoutine.fromMap(maps.first);
    }
    return null;
  }

  @override
  Future<List<AthkarRoutine>> getAll({String? userId}) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'athkar_routines',
      where: userId != null ? 'user_id = ?' : null,
      whereArgs: userId != null ? [userId] : null,
      orderBy: 'created_at DESC',
    );
    return maps.map((map) => AthkarRoutine.fromMap(map)).toList();
  }

  @override
  Future<void> update(AthkarRoutine routine) async {
    final db = await _dbHelper.database;
    await db.update(
      'athkar_routines',
      routine.toMap(),
      where: 'id = ?',
      whereArgs: [routine.id],
    );
  }

  @override
  Future<void> delete(String id) async {
    final db = await _dbHelper.database;
    await db.delete('athkar_routines', where: 'id = ?', whereArgs: [id]);
  }

  @override
  Future<List<AthkarRoutine>> search(String query, {String? userId}) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'athkar_routines',
      where: userId != null
          ? 'user_id = ? AND (title LIKE ? OR description LIKE ?)'
          : 'title LIKE ? OR description LIKE ?',
      whereArgs: userId != null
          ? [userId, '%$query%', '%$query%']
          : ['%$query%', '%$query%'],
      orderBy: 'created_at DESC',
    );
    return maps.map((map) => AthkarRoutine.fromMap(map)).toList();
  }

  Future<String> insertAthkarItem(AthkarItem item) async {
    final db = await _dbHelper.database;
    await db.insert('athkar_items', item.toMap());
    return item.id;
  }

  Future<List<AthkarItem>> getAthkarItems(String routineId) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'athkar_items',
      where: 'routine_id = ?',
      whereArgs: [routineId],
      orderBy: 'order_index ASC',
    );
    return maps.map((map) => AthkarItem.fromMap(map)).toList();
  }

  Future<void> updateAthkarItem(AthkarItem item) async {
    final db = await _dbHelper.database;
    await db.update(
      'athkar_items',
      item.toMap(),
      where: 'id = ?',
      whereArgs: [item.id],
    );
  }

  Future<void> deleteAthkarItem(String id) async {
    final db = await _dbHelper.database;
    await db.delete('athkar_items', where: 'id = ?', whereArgs: [id]);
  }
}

class AccountDatabaseService implements DatabaseService<models.Account> {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  @override
  Future<String> insert(models.Account account) async {
    final db = await _dbHelper.database;
    await db.insert('accounts', account.toMap());
    return account.id;
  }

  @override
  Future<models.Account?> getById(String id) async {
    final db = await _dbHelper.database;
    final maps = await db.query('accounts', where: 'id = ?', whereArgs: [id]);

    if (maps.isNotEmpty) {
      return models.Account.fromMap(maps.first);
    }
    return null;
  }

  @override
  Future<List<models.Account>> getAll({String? userId}) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'accounts',
      where: userId != null ? 'user_id = ? AND is_active = 1' : 'is_active = 1',
      whereArgs: userId != null ? [userId] : null,
      orderBy: 'name ASC',
    );
    return maps.map((map) => models.Account.fromMap(map)).toList();
  }

  @override
  Future<void> update(models.Account account) async {
    final db = await _dbHelper.database;
    await db.update(
      'accounts',
      account.toMap(),
      where: 'id = ?',
      whereArgs: [account.id],
    );
  }

  @override
  Future<void> delete(String id) async {
    final db = await _dbHelper.database;
    await db.update(
      'accounts',
      {'is_active': 0},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  @override
  Future<List<models.Account>> search(String query, {String? userId}) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'accounts',
      where: userId != null
          ? 'user_id = ? AND is_active = 1 AND name LIKE ?'
          : 'is_active = 1 AND name LIKE ?',
      whereArgs: userId != null ? [userId, '%$query%'] : ['%$query%'],
      orderBy: 'name ASC',
    );
    return maps.map((map) => models.Account.fromMap(map)).toList();
  }

  Future<double> getTotalBalance({String? userId}) async {
    final db = await _dbHelper.database;
    final result = await db.rawQuery(
      'SELECT SUM(balance) as total FROM accounts WHERE ${userId != null ? 'user_id = ? AND' : ''} is_active = 1',
      userId != null ? [userId] : [],
    );
    return (result.first['total'] as num?)?.toDouble() ?? 0.0;
  }
}

class CategoryDatabaseService implements DatabaseService<models.Category> {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  @override
  Future<String> insert(models.Category category) async {
    final db = await _dbHelper.database;
    await db.insert('categories', category.toMap());
    return category.id;
  }

  @override
  Future<models.Category?> getById(String id) async {
    final db = await _dbHelper.database;
    final maps = await db.query('categories', where: 'id = ?', whereArgs: [id]);

    if (maps.isNotEmpty) {
      return models.Category.fromMap(maps.first);
    }
    return null;
  }

  @override
  Future<List<models.Category>> getAll({String? userId}) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'categories',
      where: userId != null ? 'user_id = ? OR user_id IS NULL' : null,
      whereArgs: userId != null ? [userId] : null,
      orderBy: 'name ASC',
    );
    return maps.map((map) => models.Category.fromMap(map)).toList();
  }

  @override
  Future<void> update(models.Category category) async {
    final db = await _dbHelper.database;
    await db.update(
      'categories',
      category.toMap(),
      where: 'id = ?',
      whereArgs: [category.id],
    );
  }

  @override
  Future<void> delete(String id) async {
    final db = await _dbHelper.database;
    await db.update(
      'categories',
      {'is_active': 0},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  @override
  Future<List<models.Category>> search(String query, {String? userId}) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'categories',
      where: userId != null
          ? '(user_id = ? OR user_id IS NULL) AND name LIKE ?'
          : 'name LIKE ?',
      whereArgs: userId != null ? [userId, '%$query%'] : ['%$query%'],
      orderBy: 'name ASC',
    );
    return maps.map((map) => models.Category.fromMap(map)).toList();
  }

  Future<List<models.Category>> getByType(String type, {String? userId}) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'categories',
      where: userId != null
          ? '(user_id = ? OR user_id IS NULL) AND type = ? AND is_active = 1'
          : 'type = ? AND is_active = 1',
      whereArgs: userId != null ? [userId, type] : [type],
      orderBy: 'name ASC',
    );
    return maps.map((map) => models.Category.fromMap(map)).toList();
  }
}

class TransactionDatabaseService
    implements DatabaseService<models.Transaction> {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  @override
  Future<String> insert(models.Transaction transaction) async {
    final db = await _dbHelper.database;
    await db.insert('transactions', transaction.toMap());
    return transaction.id;
  }

  @override
  Future<models.Transaction?> getById(String id) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'transactions',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return models.Transaction.fromMap(maps.first);
    }
    return null;
  }

  @override
  Future<List<models.Transaction>> getAll({String? userId}) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'transactions',
      where: userId != null ? 'user_id = ?' : null,
      whereArgs: userId != null ? [userId] : null,
      orderBy: 'date DESC, created_at DESC',
    );
    return maps.map((map) => models.Transaction.fromMap(map)).toList();
  }

  @override
  Future<void> update(models.Transaction transaction) async {
    final db = await _dbHelper.database;
    await db.update(
      'transactions',
      transaction.toMap(),
      where: 'id = ?',
      whereArgs: [transaction.id],
    );
  }

  @override
  Future<void> delete(String id) async {
    final db = await _dbHelper.database;
    await db.delete('transactions', where: 'id = ?', whereArgs: [id]);
  }

  @override
  Future<List<models.Transaction>> search(
    String query, {
    String? userId,
  }) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'transactions',
      where: userId != null
          ? 'user_id = ? AND description LIKE ?'
          : 'description LIKE ?',
      whereArgs: userId != null ? [userId, '%$query%'] : ['%$query%'],
      orderBy: 'date DESC, created_at DESC',
    );
    return maps.map((map) => models.Transaction.fromMap(map)).toList();
  }

  Future<List<models.Transaction>> getByDateRange(
    DateTime startDate,
    DateTime endDate, {
    String? userId,
  }) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'transactions',
      where: userId != null
          ? 'user_id = ? AND date >= ? AND date <= ?'
          : 'date >= ? AND date <= ?',
      whereArgs: userId != null
          ? [userId, startDate.toIso8601String(), endDate.toIso8601String()]
          : [startDate.toIso8601String(), endDate.toIso8601String()],
      orderBy: 'date DESC, created_at DESC',
    );
    return maps.map((map) => models.Transaction.fromMap(map)).toList();
  }

  Future<List<models.Transaction>> getByAccount(
    String accountId, {
    String? userId,
  }) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'transactions',
      where: userId != null
          ? 'user_id = ? AND account_id = ?'
          : 'account_id = ?',
      whereArgs: userId != null ? [userId, accountId] : [accountId],
      orderBy: 'date DESC, created_at DESC',
    );
    return maps.map((map) => models.Transaction.fromMap(map)).toList();
  }

  Future<List<models.Transaction>> getByCategory(
    String categoryId, {
    String? userId,
  }) async {
    final db = await _dbHelper.database;
    final maps = await db.query(
      'transactions',
      where: userId != null
          ? 'user_id = ? AND category_id = ?'
          : 'category_id = ?',
      whereArgs: userId != null ? [userId, categoryId] : [categoryId],
      orderBy: 'date DESC, created_at DESC',
    );
    return maps.map((map) => models.Transaction.fromMap(map)).toList();
  }

  Future<double> getTotalByType(
    String type, {
    String? userId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final db = await _dbHelper.database;
    String whereClause = 'type = ?';
    List<dynamic> whereArgs = [type];

    if (userId != null) {
      whereClause = 'user_id = ? AND $whereClause';
      whereArgs.insert(0, userId);
    }

    if (startDate != null && endDate != null) {
      whereClause += ' AND date >= ? AND date <= ?';
      whereArgs.addAll([
        startDate.toIso8601String(),
        endDate.toIso8601String(),
      ]);
    }

    final result = await db.rawQuery(
      'SELECT SUM(amount) as total FROM transactions WHERE $whereClause',
      whereArgs,
    );
    return (result.first['total'] as num?)?.toDouble() ?? 0.0;
  }

  Future<Map<String, double>> getMonthlyTotals({
    String? userId,
    int? year,
  }) async {
    final db = await _dbHelper.database;
    final currentYear = year ?? DateTime.now().year;

    String whereClause = "strftime('%Y', date) = ?";
    List<dynamic> whereArgs = [currentYear.toString()];

    if (userId != null) {
      whereClause = 'user_id = ? AND $whereClause';
      whereArgs.insert(0, userId);
    }

    final result = await db.rawQuery('''
      SELECT
        strftime('%m', date) as month,
        type,
        SUM(amount) as total
      FROM transactions
      WHERE $whereClause
      GROUP BY strftime('%m', date), type
    ''', whereArgs);

    final Map<String, double> monthlyTotals = {};

    for (final row in result) {
      final month = row['month'] as String;
      final type = row['type'] as String;
      final total = (row['total'] as num).toDouble();

      final key = '${month}_$type';
      monthlyTotals[key] = total;
    }

    return monthlyTotals;
  }
}
