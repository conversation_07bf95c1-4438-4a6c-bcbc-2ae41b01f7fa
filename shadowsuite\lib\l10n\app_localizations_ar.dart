// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'شادو سويت';

  @override
  String get dashboard => 'لوحة التحكم';

  @override
  String get memoSuite => 'مجموعة المذكرات';

  @override
  String get athkarPro => 'أذكار برو';

  @override
  String get moneyFlow => 'تدفق المال';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get settings => 'الإعدادات';

  @override
  String get sync => 'مزامنة';

  @override
  String get language => 'اللغة';

  @override
  String get english => 'الإنجليزية';

  @override
  String get arabic => 'العربية';

  @override
  String get darkMode => 'الوضع المظلم';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get permissions => 'الأذونات';

  @override
  String get about => 'حول';

  @override
  String get version => 'الإصدار';

  @override
  String get record => 'تسجيل';

  @override
  String get stop => 'إيقاف';

  @override
  String get play => 'تشغيل';

  @override
  String get pause => 'إيقاف مؤقت';

  @override
  String get save => 'حفظ';

  @override
  String get cancel => 'إلغاء';

  @override
  String get delete => 'حذف';

  @override
  String get edit => 'تعديل';

  @override
  String get add => 'إضافة';

  @override
  String get search => 'بحث';

  @override
  String get filter => 'تصفية';

  @override
  String get export => 'تصدير';

  @override
  String get share => 'مشاركة';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get error => 'خطأ';

  @override
  String get success => 'نجح';

  @override
  String get warning => 'تحذير';

  @override
  String get info => 'معلومات';

  @override
  String get confirm => 'تأكيد';

  @override
  String get yes => 'نعم';

  @override
  String get no => 'لا';

  @override
  String get ok => 'موافق';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get offline => 'غير متصل';

  @override
  String get online => 'متصل';

  @override
  String get syncing => 'جاري المزامنة...';

  @override
  String get syncComplete => 'اكتملت المزامنة';

  @override
  String get syncFailed => 'فشلت المزامنة';

  @override
  String get noData => 'لا توجد بيانات متاحة';

  @override
  String get noResults => 'لم يتم العثور على نتائج';

  @override
  String get selectDate => 'اختر التاريخ';

  @override
  String get selectTime => 'اختر الوقت';

  @override
  String get today => 'اليوم';

  @override
  String get yesterday => 'أمس';

  @override
  String get thisWeek => 'هذا الأسبوع';

  @override
  String get thisMonth => 'هذا الشهر';

  @override
  String get thisYear => 'هذا العام';

  @override
  String get total => 'المجموع';

  @override
  String get income => 'الدخل';

  @override
  String get expense => 'المصروف';

  @override
  String get balance => 'الرصيد';

  @override
  String get amount => 'المبلغ';

  @override
  String get category => 'الفئة';

  @override
  String get description => 'الوصف';

  @override
  String get date => 'التاريخ';

  @override
  String get time => 'الوقت';

  @override
  String get account => 'الحساب';

  @override
  String get accounts => 'الحسابات';

  @override
  String get categories => 'الفئات';

  @override
  String get transactions => 'المعاملات';

  @override
  String get recurring => 'متكررة';

  @override
  String get budgets => 'الميزانيات';

  @override
  String get reports => 'التقارير';

  @override
  String get budget => 'الميزانية';

  @override
  String get spent => 'المنفق';

  @override
  String get remaining => 'المتبقي';

  @override
  String get exceeded => 'تم تجاوزه';

  @override
  String get daily => 'يومي';

  @override
  String get weekly => 'أسبوعي';

  @override
  String get monthly => 'شهري';

  @override
  String get yearly => 'سنوي';

  @override
  String get athkar => 'أذكار';

  @override
  String get routine => 'روتين';

  @override
  String get routines => 'روتينات';

  @override
  String get counter => 'عداد';

  @override
  String get target => 'الهدف';

  @override
  String get progress => 'التقدم';

  @override
  String get completed => 'مكتمل';

  @override
  String get remaining_count => 'المتبقي';

  @override
  String get reset => 'إعادة تعيين';

  @override
  String get duplicate => 'نسخ';

  @override
  String get customize => 'تخصيص';

  @override
  String get reminder => 'تذكير';

  @override
  String get reminders => 'التذكيرات';

  @override
  String get prayer => 'صلاة';

  @override
  String get prayers => 'الصلوات';

  @override
  String get fajr => 'الفجر';

  @override
  String get dhuhr => 'الظهر';

  @override
  String get asr => 'العصر';

  @override
  String get maghrib => 'المغرب';

  @override
  String get isha => 'العشاء';

  @override
  String get memo => 'مذكرة';

  @override
  String get memos => 'المذكرات';

  @override
  String get recording => 'تسجيل';

  @override
  String get recordings => 'التسجيلات';

  @override
  String get transcription => 'النسخ';

  @override
  String get transcriptions => 'النسخ';

  @override
  String get duration => 'المدة';

  @override
  String get fileSize => 'حجم الملف';

  @override
  String get quality => 'الجودة';

  @override
  String get microphone => 'الميكروفون';

  @override
  String get speaker => 'السماعة';

  @override
  String get volume => 'الصوت';

  @override
  String get playbackSpeed => 'سرعة التشغيل';

  @override
  String get startRecording => 'بدء التسجيل';

  @override
  String get stopRecording => 'إيقاف التسجيل';

  @override
  String get pauseRecording => 'إيقاف التسجيل مؤقتاً';

  @override
  String get resumeRecording => 'استئناف التسجيل';

  @override
  String get playRecording => 'تشغيل التسجيل';

  @override
  String get stopPlayback => 'إيقاف التشغيل';

  @override
  String get pausePlayback => 'إيقاف التشغيل مؤقتاً';

  @override
  String get resumePlayback => 'استئناف التشغيل';

  @override
  String get transcribe => 'نسخ';

  @override
  String get transcribing => 'جاري النسخ...';

  @override
  String get transcriptionComplete => 'اكتمل النسخ';

  @override
  String get transcriptionFailed => 'فشل النسخ';

  @override
  String get noTranscription => 'لا يوجد نسخ متاح';

  @override
  String get permissionRequired => 'إذن مطلوب';

  @override
  String get microphonePermission => 'إذن الميكروفون مطلوب لتسجيل الصوت';

  @override
  String get storagePermission => 'إذن التخزين مطلوب لحفظ التسجيلات';

  @override
  String get notificationPermission => 'إذن الإشعارات مطلوب للتذكيرات';

  @override
  String get grantPermission => 'منح الإذن';

  @override
  String get openSettings => 'فتح الإعدادات';

  @override
  String get personalInformation => 'Personal Information';

  @override
  String get name => 'Name';

  @override
  String get email => 'Email';

  @override
  String get changePassword => 'Change Password';

  @override
  String get currentPassword => 'Current Password';

  @override
  String get newPassword => 'New Password';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get updatePassword => 'Update Password';

  @override
  String get saveChanges => 'Save Changes';

  @override
  String get signOut => 'Sign Out';
}
