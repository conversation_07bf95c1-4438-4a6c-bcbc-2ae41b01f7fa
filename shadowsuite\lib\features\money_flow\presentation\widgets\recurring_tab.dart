import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class RecurringTab extends ConsumerWidget {
  const RecurringTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.repeat_outlined, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text('Recurring Tab', style: TextStyle(fontSize: 24, color: Colors.grey)),
          SizedBox(height: 8),
          Text('Full implementation coming soon', style: TextStyle(color: Colors.grey)),
        ],
      ),
    );
  }
}
