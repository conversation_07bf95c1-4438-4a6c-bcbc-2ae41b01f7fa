import '../constants/app_constants.dart';

class ValidationUtils {
  // Email validation
  static bool isValidEmail(String email) {
    if (email.isEmpty) return false;
    
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return emailRegex.hasMatch(email);
  }

  // Password validation
  static bool isValidPassword(String password) {
    if (password.isEmpty) return false;
    if (password.length < 8) return false;
    
    // At least one uppercase letter
    if (!password.contains(RegExp(r'[A-Z]'))) return false;
    
    // At least one lowercase letter
    if (!password.contains(RegExp(r'[a-z]'))) return false;
    
    // At least one digit
    if (!password.contains(RegExp(r'[0-9]'))) return false;
    
    return true;
  }

  static String? getPasswordValidationMessage(String password) {
    if (password.isEmpty) return 'Password is required';
    if (password.length < 8) return 'Password must be at least 8 characters';
    if (!password.contains(RegExp(r'[A-Z]'))) return 'Password must contain at least one uppercase letter';
    if (!password.contains(RegExp(r'[a-z]'))) return 'Password must contain at least one lowercase letter';
    if (!password.contains(RegExp(r'[0-9]'))) return 'Password must contain at least one number';
    return null;
  }

  // Name validation
  static bool isValidName(String name) {
    if (name.isEmpty) return false;
    if (name.length < 2) return false;
    if (name.length > 50) return false;
    
    // Only letters, spaces, hyphens, and apostrophes
    final nameRegex = RegExp(r"^[a-zA-Z\s\-']+$");
    return nameRegex.hasMatch(name);
  }

  // Phone number validation
  static bool isValidPhoneNumber(String phoneNumber) {
    if (phoneNumber.isEmpty) return false;
    
    // Remove all non-digit characters
    final digitsOnly = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    // Check if it's a valid length (10-15 digits)
    return digitsOnly.length >= 10 && digitsOnly.length <= 15;
  }

  // Amount validation for financial transactions
  static bool isValidAmount(String amount) {
    if (amount.isEmpty) return false;
    
    final amountRegex = RegExp(r'^\d+(\.\d{1,2})?$');
    if (!amountRegex.hasMatch(amount)) return false;
    
    final value = double.tryParse(amount);
    return value != null && value > 0 && value <= 999999999.99;
  }

  static double? parseAmount(String amount) {
    if (!isValidAmount(amount)) return null;
    return double.tryParse(amount);
  }

  // Text length validation
  static bool isValidMemoTitle(String title) {
    return title.isNotEmpty && title.length <= AppConstants.maxMemoTitleLength;
  }

  static bool isValidMemoDescription(String description) {
    return description.length <= AppConstants.maxMemoDescriptionLength;
  }

  static bool isValidAthkarTitle(String title) {
    return title.isNotEmpty && title.length <= AppConstants.maxAthkarTitleLength;
  }

  static bool isValidTransactionDescription(String description) {
    return description.length <= AppConstants.maxTransactionDescriptionLength;
  }

  static bool isValidCategoryName(String name) {
    return name.isNotEmpty && name.length <= AppConstants.maxCategoryNameLength;
  }

  static bool isValidAccountName(String name) {
    return name.isNotEmpty && name.length <= AppConstants.maxAccountNameLength;
  }

  // Arabic text validation
  static bool containsArabicText(String text) {
    final arabicRegex = RegExp(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]');
    return arabicRegex.hasMatch(text);
  }

  static bool isValidArabicText(String text) {
    if (text.isEmpty) return false;
    return containsArabicText(text);
  }

  // URL validation
  static bool isValidUrl(String url) {
    if (url.isEmpty) return false;
    
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  // File path validation
  static bool isValidFilePath(String path) {
    if (path.isEmpty) return false;
    
    // Check for invalid characters
    final invalidChars = RegExp(r'[<>:"|?*]');
    return !invalidChars.hasMatch(path);
  }

  // Date validation
  static bool isValidDateString(String dateString) {
    if (dateString.isEmpty) return false;
    
    try {
      DateTime.parse(dateString);
      return true;
    } catch (e) {
      return false;
    }
  }

  static bool isValidTimeString(String timeString) {
    if (timeString.isEmpty) return false;
    
    final timeRegex = RegExp(r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$');
    return timeRegex.hasMatch(timeString);
  }

  // Number validation
  static bool isValidInteger(String value) {
    if (value.isEmpty) return false;
    return int.tryParse(value) != null;
  }

  static bool isValidPositiveInteger(String value) {
    if (!isValidInteger(value)) return false;
    final intValue = int.parse(value);
    return intValue > 0;
  }

  static bool isValidDouble(String value) {
    if (value.isEmpty) return false;
    return double.tryParse(value) != null;
  }

  static bool isValidPositiveDouble(String value) {
    if (!isValidDouble(value)) return false;
    final doubleValue = double.parse(value);
    return doubleValue > 0;
  }

  // Range validation
  static bool isInRange(int value, int min, int max) {
    return value >= min && value <= max;
  }

  static bool isInRangeDouble(double value, double min, double max) {
    return value >= min && value <= max;
  }

  // Count validation for athkar
  static bool isValidAthkarCount(String count) {
    if (!isValidPositiveInteger(count)) return false;
    final intValue = int.parse(count);
    return intValue >= 1 && intValue <= 10000;
  }

  // Target count validation
  static bool isValidTargetCount(String count) {
    if (!isValidPositiveInteger(count)) return false;
    final intValue = int.parse(count);
    return intValue >= 1 && intValue <= 1000;
  }

  // Recording duration validation
  static bool isValidRecordingDuration(int durationInSeconds) {
    return durationInSeconds > 0 && durationInSeconds <= AppConstants.maxRecordingDuration;
  }

  // File size validation
  static bool isValidFileSize(int fileSizeInBytes) {
    const maxFileSize = 100 * 1024 * 1024; // 100 MB
    return fileSizeInBytes > 0 && fileSizeInBytes <= maxFileSize;
  }

  // Currency validation
  static bool isValidCurrency(String currency) {
    if (currency.isEmpty) return false;
    
    // Common currency codes (ISO 4217)
    final validCurrencies = [
      'USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD', 'CHF', 'CNY', 'SEK', 'NZD',
      'MXN', 'SGD', 'HKD', 'NOK', 'TRY', 'ZAR', 'BRL', 'INR', 'KRW', 'PLN',
      'AED', 'SAR', 'EGP', 'QAR', 'KWD', 'BHD', 'OMR', 'JOD', 'LBP', 'IQD'
    ];
    
    return validCurrencies.contains(currency.toUpperCase());
  }

  // Account type validation
  static bool isValidAccountType(String type) {
    return AppConstants.accountTypes.contains(type);
  }

  // Category type validation
  static bool isValidCategoryType(String type) {
    return type == 'income' || type == 'expense';
  }

  // Frequency validation for recurring transactions
  static bool isValidFrequency(String frequency) {
    const validFrequencies = ['daily', 'weekly', 'monthly', 'yearly'];
    return validFrequencies.contains(frequency.toLowerCase());
  }

  // Period validation for budgets
  static bool isValidBudgetPeriod(String period) {
    const validPeriods = ['daily', 'weekly', 'monthly', 'yearly'];
    return validPeriods.contains(period.toLowerCase());
  }

  // Reminder time validation
  static bool isValidReminderTime(String time) {
    return isValidTimeString(time);
  }

  // Multiple reminder times validation
  static bool isValidReminderTimes(List<String> times) {
    if (times.isEmpty) return true; // Empty list is valid (no reminders)
    if (times.length > 10) return false; // Max 10 reminder times
    
    for (final time in times) {
      if (!isValidReminderTime(time)) return false;
    }
    
    return true;
  }

  // Search query validation
  static bool isValidSearchQuery(String query) {
    if (query.isEmpty) return false;
    if (query.length < 2) return false;
    if (query.length > 100) return false;
    
    // Remove excessive whitespace
    final trimmed = query.trim().replaceAll(RegExp(r'\s+'), ' ');
    return trimmed.length >= 2;
  }

  // Sanitize search query
  static String sanitizeSearchQuery(String query) {
    return query.trim().replaceAll(RegExp(r'\s+'), ' ');
  }

  // General text sanitization
  static String sanitizeText(String text) {
    return text.trim().replaceAll(RegExp(r'\s+'), ' ');
  }

  // Remove special characters for safe storage
  static String sanitizeForStorage(String text) {
    return text.replaceAll(RegExp(r'[^\w\s\-_.]'), '');
  }

  // Validation result class
  static ValidationResult validateField(String value, List<ValidationRule> rules) {
    for (final rule in rules) {
      if (!rule.validator(value)) {
        return ValidationResult(isValid: false, errorMessage: rule.errorMessage);
      }
    }
    return ValidationResult(isValid: true);
  }
}

class ValidationRule {
  final bool Function(String) validator;
  final String errorMessage;

  ValidationRule({
    required this.validator,
    required this.errorMessage,
  });
}

class ValidationResult {
  final bool isValid;
  final String? errorMessage;

  ValidationResult({
    required this.isValid,
    this.errorMessage,
  });
}
