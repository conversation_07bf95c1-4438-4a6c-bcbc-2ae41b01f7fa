// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'ShadowSuite';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get memoSuite => 'Memo Suite';

  @override
  String get athkarPro => 'Athkar Pro';

  @override
  String get moneyFlow => 'Money Flow';

  @override
  String get profile => 'Profile';

  @override
  String get settings => 'Settings';

  @override
  String get sync => 'Sync';

  @override
  String get language => 'Language';

  @override
  String get english => 'English';

  @override
  String get arabic => 'Arabic';

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get notifications => 'Notifications';

  @override
  String get permissions => 'Permissions';

  @override
  String get about => 'About';

  @override
  String get version => 'Version';

  @override
  String get record => 'Record';

  @override
  String get stop => 'Stop';

  @override
  String get play => 'Play';

  @override
  String get pause => 'Pause';

  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get delete => 'Delete';

  @override
  String get edit => 'Edit';

  @override
  String get add => 'Add';

  @override
  String get search => 'Search';

  @override
  String get filter => 'Filter';

  @override
  String get export => 'Export';

  @override
  String get share => 'Share';

  @override
  String get loading => 'Loading...';

  @override
  String get error => 'Error';

  @override
  String get success => 'Success';

  @override
  String get warning => 'Warning';

  @override
  String get info => 'Information';

  @override
  String get confirm => 'Confirm';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get ok => 'OK';

  @override
  String get retry => 'Retry';

  @override
  String get offline => 'Offline';

  @override
  String get online => 'Online';

  @override
  String get syncing => 'Syncing...';

  @override
  String get syncComplete => 'Sync Complete';

  @override
  String get syncFailed => 'Sync Failed';

  @override
  String get noData => 'No data available';

  @override
  String get noResults => 'No results found';

  @override
  String get selectDate => 'Select Date';

  @override
  String get selectTime => 'Select Time';

  @override
  String get today => 'Today';

  @override
  String get yesterday => 'Yesterday';

  @override
  String get thisWeek => 'This Week';

  @override
  String get thisMonth => 'This Month';

  @override
  String get thisYear => 'This Year';

  @override
  String get total => 'Total';

  @override
  String get income => 'Income';

  @override
  String get expense => 'Expense';

  @override
  String get balance => 'Balance';

  @override
  String get amount => 'Amount';

  @override
  String get category => 'Category';

  @override
  String get description => 'Description';

  @override
  String get date => 'Date';

  @override
  String get time => 'Time';

  @override
  String get account => 'Account';

  @override
  String get accounts => 'Accounts';

  @override
  String get categories => 'Categories';

  @override
  String get transactions => 'Transactions';

  @override
  String get recurring => 'Recurring';

  @override
  String get budgets => 'Budgets';

  @override
  String get reports => 'Reports';

  @override
  String get budget => 'Budget';

  @override
  String get spent => 'Spent';

  @override
  String get remaining => 'Remaining';

  @override
  String get exceeded => 'Exceeded';

  @override
  String get daily => 'Daily';

  @override
  String get weekly => 'Weekly';

  @override
  String get monthly => 'Monthly';

  @override
  String get yearly => 'Yearly';

  @override
  String get athkar => 'Athkar';

  @override
  String get routine => 'Routine';

  @override
  String get routines => 'Routines';

  @override
  String get counter => 'Counter';

  @override
  String get target => 'Target';

  @override
  String get progress => 'Progress';

  @override
  String get completed => 'Completed';

  @override
  String get remaining_count => 'Remaining';

  @override
  String get reset => 'Reset';

  @override
  String get duplicate => 'Duplicate';

  @override
  String get customize => 'Customize';

  @override
  String get reminder => 'Reminder';

  @override
  String get reminders => 'Reminders';

  @override
  String get prayer => 'Prayer';

  @override
  String get prayers => 'Prayers';

  @override
  String get fajr => 'Fajr';

  @override
  String get dhuhr => 'Dhuhr';

  @override
  String get asr => 'Asr';

  @override
  String get maghrib => 'Maghrib';

  @override
  String get isha => 'Isha';

  @override
  String get memo => 'Memo';

  @override
  String get memos => 'Memos';

  @override
  String get recording => 'Recording';

  @override
  String get recordings => 'Recordings';

  @override
  String get transcription => 'Transcription';

  @override
  String get transcriptions => 'Transcriptions';

  @override
  String get duration => 'Duration';

  @override
  String get fileSize => 'File Size';

  @override
  String get quality => 'Quality';

  @override
  String get microphone => 'Microphone';

  @override
  String get speaker => 'Speaker';

  @override
  String get volume => 'Volume';

  @override
  String get playbackSpeed => 'Playback Speed';

  @override
  String get startRecording => 'Start Recording';

  @override
  String get stopRecording => 'Stop Recording';

  @override
  String get pauseRecording => 'Pause Recording';

  @override
  String get resumeRecording => 'Resume Recording';

  @override
  String get playRecording => 'Play Recording';

  @override
  String get stopPlayback => 'Stop Playback';

  @override
  String get pausePlayback => 'Pause Playback';

  @override
  String get resumePlayback => 'Resume Playback';

  @override
  String get transcribe => 'Transcribe';

  @override
  String get transcribing => 'Transcribing...';

  @override
  String get transcriptionComplete => 'Transcription Complete';

  @override
  String get transcriptionFailed => 'Transcription Failed';

  @override
  String get noTranscription => 'No transcription available';

  @override
  String get permissionRequired => 'Permission Required';

  @override
  String get microphonePermission =>
      'Microphone permission is required to record audio';

  @override
  String get storagePermission =>
      'Storage permission is required to save recordings';

  @override
  String get notificationPermission =>
      'Notification permission is required for reminders';

  @override
  String get grantPermission => 'Grant Permission';

  @override
  String get openSettings => 'Open Settings';
}
