import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/models/memo.dart';
import '../../../../core/services/audio_service.dart';
import '../../../../core/services/database_service.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../../../shared/theme/app_theme.dart';
import '../../../../core/utils/date_utils.dart';
import 'memo_list_tab.dart';
import 'recording_tab.dart';

final memosWithAudioProvider = FutureProvider<List<Memo>>((ref) async {
  final memoService = MemoDatabaseService();
  final allMemos = await memoService.getAll();
  return allMemos.where((memo) => memo.hasAudio).toList();
});

final selectedMemoProvider = StateProvider<Memo?>((ref) => null);

final transcriptionProvider = StateProvider<String?>((ref) => null);

final isTranscribingProvider = StateProvider<bool>((ref) => false);

class TranscriptionTab extends ConsumerStatefulWidget {
  const TranscriptionTab({super.key});

  @override
  ConsumerState<TranscriptionTab> createState() => _TranscriptionTabState();
}

class _TranscriptionTabState extends ConsumerState<TranscriptionTab> {
  final TextEditingController _transcriptionController =
      TextEditingController();
  bool _isSaving = false;

  @override
  void dispose() {
    _transcriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final memosWithAudio = ref.watch(memosWithAudioProvider);
    final selectedMemo = ref.watch(selectedMemoProvider);
    final transcription = ref.watch(transcriptionProvider);
    final isTranscribing = ref.watch(isTranscribingProvider);

    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Memo Selection
          Text(
            'Select Audio Memo',
            style: Theme.of(context).textTheme.titleLarge,
          ),

          const SizedBox(height: 16),

          Expanded(
            flex: 2,
            child: memosWithAudio.when(
              data: (memos) {
                if (memos.isEmpty) {
                  return _buildEmptyState();
                }
                return _buildMemoList(memos, selectedMemo);
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Theme.of(context).colorScheme.error,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Error loading memos',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      error.toString(),
                      style: Theme.of(context).textTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Transcription Section
          if (selectedMemo != null) ...[
            Text(
              'Transcription',
              style: Theme.of(context).textTheme.titleLarge,
            ),

            const SizedBox(height: 16),

            Expanded(
              flex: 3,
              child: _buildTranscriptionSection(
                selectedMemo,
                transcription,
                isTranscribing,
              ),
            ),
          ] else
            Expanded(
              flex: 3,
              child: Center(
                child: Text(
                  'Select a memo to view or edit transcription',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withOpacity(0.6),
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.audiotrack_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'No audio memos found',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Record some audio memos first',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMemoList(List<Memo> memos, Memo? selectedMemo) {
    return ListView.builder(
      itemCount: memos.length,
      itemBuilder: (context, index) {
        final memo = memos[index];
        final isSelected = selectedMemo?.id == memo.id;

        return Card(
          margin: const EdgeInsets.only(bottom: AppConstants.defaultMargin),
          color: isSelected ? AppTheme.memoColor.withOpacity(0.1) : null,
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: AppTheme.memoColor.withOpacity(0.2),
              child: Icon(Icons.audiotrack, color: AppTheme.memoColor),
            ),
            title: Text(
              memo.title,
              style: TextStyle(
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (memo.description != null && memo.description!.isNotEmpty)
                  Text(
                    memo.description!,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      size: 14,
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withOpacity(0.6),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      memo.formattedDuration,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Text(
                      AppDateUtils.getRelativeTime(memo.createdAt),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            trailing: memo.hasTranscription
                ? Icon(Icons.transcribe, color: AppTheme.memoColor)
                : Icon(
                    Icons.transcribe_outlined,
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withOpacity(0.3),
                  ),
            onTap: () {
              ref.read(selectedMemoProvider.notifier).state = memo;
              ref.read(transcriptionProvider.notifier).state =
                  memo.transcription;
              _transcriptionController.text = memo.transcription ?? '';
            },
          ),
        );
      },
    );
  }

  Widget _buildTranscriptionSection(
    Memo memo,
    String? transcription,
    bool isTranscribing,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Memo Info
        Card(
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Row(
              children: [
                Icon(Icons.audiotrack, color: AppTheme.memoColor),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        memo.title,
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${memo.formattedDuration} • ${AppDateUtils.formatDateForDisplay(memo.createdAt)}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(
                            context,
                          ).colorScheme.onSurface.withOpacity(0.6),
                        ),
                      ),
                    ],
                  ),
                ),
                CustomIconButton(
                  icon: Icons.play_arrow,
                  onPressed: () => _playAudio(memo),
                  tooltip: 'Play audio',
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Auto-transcribe button
        if (!memo.hasTranscription)
          CustomButton(
            text: 'Auto Transcribe',
            icon: Icons.auto_awesome,
            type: ButtonType.outlined,
            isLoading: isTranscribing,
            onPressed: isTranscribing ? null : () => _autoTranscribe(memo),
          ),

        if (!memo.hasTranscription && !isTranscribing)
          const SizedBox(height: 16),

        // Transcription text field
        Expanded(
          child: CustomTextField(
            controller: _transcriptionController,
            label: 'Transcription',
            hint: memo.hasTranscription
                ? 'Edit transcription...'
                : 'Enter transcription manually or use auto-transcribe',
            type: TextFieldType.multiline,
            maxLines: null,
            onChanged: (value) {
              ref.read(transcriptionProvider.notifier).state = value;
            },
          ),
        ),

        const SizedBox(height: 16),

        // Save button
        Row(
          children: [
            Expanded(
              child: CustomButton(
                text: 'Save Transcription',
                icon: Icons.save,
                type: ButtonType.primary,
                isLoading: _isSaving,
                onPressed: _canSaveTranscription(memo, transcription)
                    ? () => _saveTranscription(memo)
                    : null,
              ),
            ),
            const SizedBox(width: 12),
            CustomButton(
              text: 'Clear',
              icon: Icons.clear,
              type: ButtonType.outlined,
              onPressed: () {
                _transcriptionController.clear();
                ref.read(transcriptionProvider.notifier).state = '';
              },
            ),
          ],
        ),
      ],
    );
  }

  bool _canSaveTranscription(Memo memo, String? transcription) {
    return transcription != null &&
        transcription.trim().isNotEmpty &&
        transcription.trim() != memo.transcription &&
        !_isSaving;
  }

  Future<void> _playAudio(Memo memo) async {
    if (memo.audioPath == null) return;

    try {
      final audioService = ref.read(audioServiceProvider);
      await audioService.playAudio(memo.audioPath!);
    } catch (e) {
      _showErrorSnackBar('Failed to play audio: $e');
    }
  }

  Future<void> _autoTranscribe(Memo memo) async {
    if (memo.audioPath == null) return;

    ref.read(isTranscribingProvider.notifier).state = true;

    try {
      final audioService = ref.read(audioServiceProvider);
      final transcription = await audioService.transcribeAudio(memo.audioPath!);

      if (transcription != null) {
        _transcriptionController.text = transcription;
        ref.read(transcriptionProvider.notifier).state = transcription;
        _showSuccessSnackBar('Transcription completed');
      } else {
        _showErrorSnackBar('Transcription failed');
      }
    } catch (e) {
      _showErrorSnackBar('Transcription error: $e');
    } finally {
      ref.read(isTranscribingProvider.notifier).state = false;
    }
  }

  Future<void> _saveTranscription(Memo memo) async {
    final transcription = ref.read(transcriptionProvider);
    if (transcription == null || transcription.trim().isEmpty) return;

    setState(() {
      _isSaving = true;
    });

    try {
      final memoService = MemoDatabaseService();
      final updatedMemo = memo.copyWith(
        transcription: transcription.trim(),
        updatedAt: DateTime.now(),
      );

      await memoService.update(updatedMemo);

      // Update the selected memo
      ref.read(selectedMemoProvider.notifier).state = updatedMemo;

      // Refresh memo lists
      ref.invalidate(memosWithAudioProvider);
      ref.invalidate(memoListProvider);

      _showSuccessSnackBar('Transcription saved successfully');
    } catch (e) {
      _showErrorSnackBar('Failed to save transcription: $e');
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }
}
