[{"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/layout/notification_action_tombstone.xml", "source": "eu.simonbinder.sqlite3_flutter_libs-core-1.13.1-7:/layout/notification_action_tombstone.xml"}, {"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/layout/notification_action.xml", "source": "eu.simonbinder.sqlite3_flutter_libs-core-1.13.1-7:/layout/notification_action.xml"}, {"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/layout/notification_template_part_time.xml", "source": "eu.simonbinder.sqlite3_flutter_libs-core-1.13.1-7:/layout/notification_template_part_time.xml"}, {"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/layout/custom_dialog.xml", "source": "eu.simonbinder.sqlite3_flutter_libs-core-1.13.1-7:/layout/custom_dialog.xml"}, {"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/layout/ime_base_split_test_activity.xml", "source": "eu.simonbinder.sqlite3_flutter_libs-core-1.13.1-7:/layout/ime_base_split_test_activity.xml"}, {"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/layout/notification_template_icon_group.xml", "source": "eu.simonbinder.sqlite3_flutter_libs-core-1.13.1-7:/layout/notification_template_icon_group.xml"}, {"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/layout/notification_template_custom_big.xml", "source": "eu.simonbinder.sqlite3_flutter_libs-core-1.13.1-7:/layout/notification_template_custom_big.xml"}, {"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/layout/notification_template_part_chronometer.xml", "source": "eu.simonbinder.sqlite3_flutter_libs-core-1.13.1-7:/layout/notification_template_part_chronometer.xml"}, {"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/layout/ime_secondary_split_test_activity.xml", "source": "eu.simonbinder.sqlite3_flutter_libs-core-1.13.1-7:/layout/ime_secondary_split_test_activity.xml"}]