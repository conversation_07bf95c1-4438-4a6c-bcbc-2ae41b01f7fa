[{"merged": "io.flutter.plugins.imagepicker.image_picker_android-release-26:/drawable/notification_bg.xml", "source": "io.flutter.plugins.imagepicker.image_picker_android-core-1.13.1-7:/drawable/notification_bg.xml"}, {"merged": "io.flutter.plugins.imagepicker.image_picker_android-release-26:/drawable/notification_bg_low.xml", "source": "io.flutter.plugins.imagepicker.image_picker_android-core-1.13.1-7:/drawable/notification_bg_low.xml"}, {"merged": "io.flutter.plugins.imagepicker.image_picker_android-release-26:/drawable/notification_tile_bg.xml", "source": "io.flutter.plugins.imagepicker.image_picker_android-core-1.13.1-7:/drawable/notification_tile_bg.xml"}, {"merged": "io.flutter.plugins.imagepicker.image_picker_android-release-26:/drawable/notification_icon_background.xml", "source": "io.flutter.plugins.imagepicker.image_picker_android-core-1.13.1-7:/drawable/notification_icon_background.xml"}]