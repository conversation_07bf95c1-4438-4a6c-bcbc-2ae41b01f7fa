import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';

import 'core/constants/app_constants.dart';
import 'core/database/database_helper.dart';
import 'core/providers/app_providers.dart';
import 'shared/theme/app_theme.dart';
import 'features/dashboard/presentation/pages/main_page.dart';
import 'l10n/app_localizations.dart';

final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Supabase
  await Supabase.initialize(
    url: AppConstants.supabaseUrl,
    anonKey: AppConstants.supabaseAnonKey,
  );

  // Initialize database
  await DatabaseHelper().database;

  // Initialize notifications
  await _initializeNotifications();

  // Request permissions
  await _requestPermissions();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
    DeviceOrientation.landscapeLeft,
    DeviceOrientation.landscapeRight,
  ]);

  runApp(const ProviderScope(child: ShadowSuiteApp()));
}

Future<void> _initializeNotifications() async {
  const AndroidInitializationSettings initializationSettingsAndroid =
      AndroidInitializationSettings('@mipmap/ic_launcher');

  const InitializationSettings initializationSettings = InitializationSettings(
    android: initializationSettingsAndroid,
  );

  await flutterLocalNotificationsPlugin.initialize(
    initializationSettings,
    onDidReceiveNotificationResponse: (NotificationResponse response) async {
      // Handle notification tap
    },
  );

  // Create notification channels
  await _createNotificationChannels();
}

Future<void> _createNotificationChannels() async {
  const AndroidNotificationChannel reminderChannel = AndroidNotificationChannel(
    AppConstants.reminderChannelId,
    'Reminders',
    description: 'Notifications for prayer and athkar reminders',
    importance: Importance.high,
  );

  const AndroidNotificationChannel syncChannel = AndroidNotificationChannel(
    AppConstants.syncChannelId,
    'Sync',
    description: 'Notifications for sync status',
    importance: Importance.low,
  );

  const AndroidNotificationChannel generalChannel = AndroidNotificationChannel(
    AppConstants.generalChannelId,
    'General',
    description: 'General app notifications',
    importance: Importance.defaultImportance,
  );

  final AndroidFlutterLocalNotificationsPlugin? androidPlugin =
      flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin
          >();

  if (androidPlugin != null) {
    await androidPlugin.createNotificationChannel(reminderChannel);
    await androidPlugin.createNotificationChannel(syncChannel);
    await androidPlugin.createNotificationChannel(generalChannel);
  }
}

Future<void> _requestPermissions() async {
  // Request microphone permission for audio recording
  await Permission.microphone.request();

  // Request storage permission for file operations
  await Permission.storage.request();

  // Request notification permission
  await Permission.notification.request();

  // Request camera permission (if needed for future features)
  await Permission.camera.request();
}

class ShadowSuiteApp extends ConsumerWidget {
  const ShadowSuiteApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(themeModeProvider);
    final locale = ref.watch(localeProvider);

    return MaterialApp(
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: themeMode,
      locale: locale,
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('en', ''), // English
        Locale('ar', ''), // Arabic
      ],
      home: const MainPage(),
      builder: (context, child) {
        return Directionality(
          textDirection: locale?.languageCode == 'ar'
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: child!,
        );
      },
    );
  }
}
