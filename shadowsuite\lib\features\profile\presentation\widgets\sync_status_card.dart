import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/services/supabase_service.dart';
import '../../../../shared/widgets/custom_button.dart';

class SyncStatusCard extends ConsumerWidget {
  final AsyncValue<SyncStatus> syncStatus;

  const SyncStatusCard({
    super.key,
    required this.syncStatus,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sync Status',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            
            syncStatus.when(
              data: (status) => _buildSyncStatusContent(context, ref, status),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Text(
                'Error: $error',
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSyncStatusContent(BuildContext context, WidgetRef ref, SyncStatus status) {
    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (status) {
      case SyncStatus.idle:
        statusColor = Colors.grey;
        statusIcon = Icons.sync_disabled;
        statusText = 'Not syncing';
        break;
      case SyncStatus.syncing:
        statusColor = Colors.blue;
        statusIcon = Icons.sync;
        statusText = 'Syncing...';
        break;
      case SyncStatus.success:
        statusColor = Colors.green;
        statusIcon = Icons.sync;
        statusText = 'Synced successfully';
        break;
      case SyncStatus.error:
        statusColor = Colors.red;
        statusIcon = Icons.sync_problem;
        statusText = 'Sync failed';
        break;
    }

    return Column(
      children: [
        Row(
          children: [
            Icon(statusIcon, color: statusColor),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                statusText,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: statusColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        Row(
          children: [
            Expanded(
              child: CustomButton(
                text: 'Sync Now',
                icon: Icons.sync,
                type: ButtonType.outlined,
                size: ButtonSize.small,
                onPressed: status == SyncStatus.syncing ? null : () {
                  final supabaseService = ref.read(supabaseServiceProvider);
                  supabaseService.syncAll();
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: CustomButton(
                text: 'View History',
                icon: Icons.history,
                type: ButtonType.text,
                size: ButtonSize.small,
                onPressed: () {
                  // Navigate to sync history
                },
              ),
            ),
          ],
        ),
      ],
    );
  }
}

// Provider for Supabase service
final supabaseServiceProvider = Provider<SupabaseService>((ref) => SupabaseService());
