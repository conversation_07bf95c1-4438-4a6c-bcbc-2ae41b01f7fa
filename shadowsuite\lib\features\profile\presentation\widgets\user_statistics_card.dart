import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/services/database_service.dart';

final userStatsProvider = FutureProvider.family<Map<String, int>, String>((ref, userId) async {
  final memoService = MemoDatabaseService();
  final athkarService = AthkarDatabaseService();
  final accountService = AccountDatabaseService();
  final transactionService = TransactionDatabaseService();

  final memoCount = (await memoService.getAll(userId: userId)).length;
  final athkarCount = (await athkarService.getAll(userId: userId)).length;
  final accountCount = (await accountService.getAll(userId: userId)).length;
  final transactionCount = (await transactionService.getAll(userId: userId)).length;

  return {
    'memos': memoCount,
    'athkar': athkarCount,
    'accounts': accountCount,
    'transactions': transactionCount,
  };
});

class UserStatisticsCard extends ConsumerWidget {
  final String userId;

  const UserStatisticsCard({
    super.key,
    required this.userId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final stats = ref.watch(userStatsProvider(userId));

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Usage Statistics',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            
            stats.when(
              data: (statistics) => _buildStatistics(context, statistics),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Text(
                'Error loading statistics: $error',
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatistics(BuildContext context, Map<String, int> stats) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildStatItem(
                context,
                'Memos',
                stats['memos'] ?? 0,
                Icons.mic,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatItem(
                context,
                'Athkar',
                stats['athkar'] ?? 0,
                Icons.auto_stories,
                Colors.green,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildStatItem(
                context,
                'Accounts',
                stats['accounts'] ?? 0,
                Icons.account_balance,
                Colors.orange,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatItem(
                context,
                'Transactions',
                stats['transactions'] ?? 0,
                Icons.receipt,
                Colors.purple,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    int count,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            count.toString(),
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
