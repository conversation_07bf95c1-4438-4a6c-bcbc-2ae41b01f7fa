import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/models/memo.dart';
import '../../../../core/services/database_service.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../../../shared/theme/app_theme.dart';
import '../../../../core/utils/date_utils.dart';

final memoListProvider = FutureProvider<List<Memo>>((ref) async {
  final memoService = MemoDatabaseService();
  return await memoService.getAll();
});

final searchQueryProvider = StateProvider<String>((ref) => '');

final filteredMemosProvider = Provider<AsyncValue<List<Memo>>>((ref) {
  final memosAsync = ref.watch(memoListProvider);
  final searchQuery = ref.watch(searchQueryProvider);

  return memosAsync.when(
    data: (memos) {
      if (searchQuery.isEmpty) {
        return AsyncValue.data(memos);
      }
      final filtered = memos.where((memo) {
        return memo.title.toLowerCase().contains(searchQuery.toLowerCase()) ||
               (memo.description?.toLowerCase().contains(searchQuery.toLowerCase()) ?? false) ||
               (memo.transcription?.toLowerCase().contains(searchQuery.toLowerCase()) ?? false);
      }).toList();
      return AsyncValue.data(filtered);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

class MemoListTab extends ConsumerStatefulWidget {
  const MemoListTab({super.key});

  @override
  ConsumerState<MemoListTab> createState() => _MemoListTabState();
}

class _MemoListTabState extends ConsumerState<MemoListTab> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final filteredMemos = ref.watch(filteredMemosProvider);

    return Column(
      children: [
        // Search Bar
        Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: CustomSearchField(
            controller: _searchController,
            hint: 'Search memos...',
            onChanged: (query) {
              ref.read(searchQueryProvider.notifier).state = query;
            },
          ),
        ),
        
        // Memo List
        Expanded(
          child: filteredMemos.when(
            data: (memos) {
              if (memos.isEmpty) {
                return _buildEmptyState();
              }
              return ListView.builder(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.defaultPadding,
                ),
                itemCount: memos.length,
                itemBuilder: (context, index) {
                  final memo = memos[index];
                  return _buildMemoCard(memo);
                },
              );
            },
            loading: () => const Center(
              child: CircularProgressIndicator(),
            ),
            error: (error, stack) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading memos',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    error.toString(),
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      ref.invalidate(memoListProvider);
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.mic_none_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'No memos yet',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start recording your first memo',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMemoCard(Memo memo) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultMargin),
      child: InkWell(
        onTap: () => _showMemoDetails(memo),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Icon(
                    memo.hasAudio ? Icons.mic : Icons.text_fields,
                    color: AppTheme.memoColor,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      memo.title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) => _handleMemoAction(value, memo),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit),
                            SizedBox(width: 8),
                            Text('Edit'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'share',
                        child: Row(
                          children: [
                            Icon(Icons.share),
                            SizedBox(width: 8),
                            Text('Share'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, color: Colors.red),
                            SizedBox(width: 8),
                            Text('Delete', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // Description
              if (memo.description != null && memo.description!.isNotEmpty)
                Text(
                  memo.description!,
                  style: Theme.of(context).textTheme.bodyMedium,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              
              // Transcription preview
              if (memo.hasTranscription)
                Container(
                  margin: const EdgeInsets.only(top: 8),
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.transcribe,
                        size: 16,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          memo.transcription!,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              
              const SizedBox(height: 8),
              
              // Footer
              Row(
                children: [
                  if (memo.hasAudio) ...[
                    Icon(
                      Icons.access_time,
                      size: 14,
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      memo.formattedDuration,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                    const SizedBox(width: 16),
                  ],
                  Icon(
                    Icons.storage,
                    size: 14,
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    memo.formattedFileSize,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                  const Spacer(),
                  Text(
                    AppDateUtils.getRelativeTime(memo.createdAt),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showMemoDetails(Memo memo) {
    // Navigate to memo details page
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(memo.title),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (memo.description != null && memo.description!.isNotEmpty) ...[
                Text(
                  'Description:',
                  style: Theme.of(context).textTheme.titleSmall,
                ),
                const SizedBox(height: 4),
                Text(memo.description!),
                const SizedBox(height: 16),
              ],
              if (memo.hasTranscription) ...[
                Text(
                  'Transcription:',
                  style: Theme.of(context).textTheme.titleSmall,
                ),
                const SizedBox(height: 4),
                Text(memo.transcription!),
                const SizedBox(height: 16),
              ],
              Text(
                'Created: ${AppDateUtils.formatDateTimeForDisplay(memo.createdAt)}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
              if (memo.hasAudio) ...[
                const SizedBox(height: 4),
                Text(
                  'Duration: ${memo.formattedDuration}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                const SizedBox(height: 4),
                Text(
                  'File Size: ${memo.formattedFileSize}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          if (memo.hasAudio)
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                // Play audio
              },
              child: const Text('Play'),
            ),
        ],
      ),
    );
  }

  void _handleMemoAction(String action, Memo memo) {
    switch (action) {
      case 'edit':
        // Navigate to edit memo
        break;
      case 'share':
        // Share memo
        break;
      case 'delete':
        _showDeleteConfirmation(memo);
        break;
    }
  }

  void _showDeleteConfirmation(Memo memo) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Memo'),
        content: Text('Are you sure you want to delete "${memo.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _deleteMemo(memo);
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteMemo(Memo memo) async {
    try {
      final memoService = MemoDatabaseService();
      await memoService.delete(memo.id);
      ref.invalidate(memoListProvider);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Memo deleted successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error deleting memo: $e')),
        );
      }
    }
  }
}
