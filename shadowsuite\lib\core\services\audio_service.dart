import 'dart:io';
import 'dart:async';
import 'package:record/record.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../constants/app_constants.dart';

enum RecordingState {
  idle,
  recording,
  paused,
  stopped,
}

enum PlaybackState {
  idle,
  playing,
  paused,
  stopped,
}

class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  final AudioRecorder _recorder = AudioRecorder();
  final AudioPlayer _player = AudioPlayer();
  final SpeechToText _speechToText = SpeechToText();

  RecordingState _recordingState = RecordingState.idle;
  PlaybackState _playbackState = PlaybackState.idle;
  
  String? _currentRecordingPath;
  String? _currentPlayingPath;
  Duration _recordingDuration = Duration.zero;
  Duration _playbackDuration = Duration.zero;
  Duration _playbackPosition = Duration.zero;

  Timer? _recordingTimer;
  StreamSubscription? _playerSubscription;

  // Getters
  RecordingState get recordingState => _recordingState;
  PlaybackState get playbackState => _playbackState;
  String? get currentRecordingPath => _currentRecordingPath;
  String? get currentPlayingPath => _currentPlayingPath;
  Duration get recordingDuration => _recordingDuration;
  Duration get playbackDuration => _playbackDuration;
  Duration get playbackPosition => _playbackPosition;

  // Stream controllers for state changes
  final StreamController<RecordingState> _recordingStateController = 
      StreamController<RecordingState>.broadcast();
  final StreamController<PlaybackState> _playbackStateController = 
      StreamController<PlaybackState>.broadcast();
  final StreamController<Duration> _recordingDurationController = 
      StreamController<Duration>.broadcast();
  final StreamController<Duration> _playbackPositionController = 
      StreamController<Duration>.broadcast();

  // Streams
  Stream<RecordingState> get recordingStateStream => _recordingStateController.stream;
  Stream<PlaybackState> get playbackStateStream => _playbackStateController.stream;
  Stream<Duration> get recordingDurationStream => _recordingDurationController.stream;
  Stream<Duration> get playbackPositionStream => _playbackPositionController.stream;

  Future<void> initialize() async {
    await _initializePlayer();
    await _initializeSpeechToText();
  }

  Future<void> _initializePlayer() async {
    _playerSubscription = _player.onPositionChanged.listen((position) {
      _playbackPosition = position;
      _playbackPositionController.add(position);
    });

    _player.onDurationChanged.listen((duration) {
      _playbackDuration = duration;
    });

    _player.onPlayerStateChanged.listen((state) {
      switch (state) {
        case PlayerState.playing:
          _updatePlaybackState(PlaybackState.playing);
          break;
        case PlayerState.paused:
          _updatePlaybackState(PlaybackState.paused);
          break;
        case PlayerState.stopped:
          _updatePlaybackState(PlaybackState.stopped);
          break;
        case PlayerState.completed:
          _updatePlaybackState(PlaybackState.idle);
          break;
        default:
          break;
      }
    });
  }

  Future<void> _initializeSpeechToText() async {
    await _speechToText.initialize(
      onError: (error) {
        print('Speech to text error: $error');
      },
      onStatus: (status) {
        print('Speech to text status: $status');
      },
    );
  }

  Future<bool> checkPermissions() async {
    final microphoneStatus = await Permission.microphone.status;
    final storageStatus = await Permission.storage.status;

    if (microphoneStatus.isDenied) {
      final result = await Permission.microphone.request();
      if (result.isDenied) return false;
    }

    if (storageStatus.isDenied) {
      final result = await Permission.storage.request();
      if (result.isDenied) return false;
    }

    return true;
  }

  Future<String> _generateRecordingPath() async {
    final directory = await getApplicationDocumentsDirectory();
    final recordingsDir = Directory(path.join(directory.path, AppConstants.recordingsPath));
    
    if (!await recordingsDir.exists()) {
      await recordingsDir.create(recursive: true);
    }

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final fileName = 'recording_$timestamp.${AppConstants.audioFormat}';
    return path.join(recordingsDir.path, fileName);
  }

  Future<bool> startRecording() async {
    if (_recordingState != RecordingState.idle) return false;

    final hasPermission = await checkPermissions();
    if (!hasPermission) return false;

    try {
      _currentRecordingPath = await _generateRecordingPath();
      
      await _recorder.start(
        const RecordConfig(
          encoder: AudioEncoder.aacLc,
          bitRate: AppConstants.audioBitRate,
          sampleRate: AppConstants.audioSampleRate,
        ),
        path: _currentRecordingPath!,
      );

      _recordingDuration = Duration.zero;
      _startRecordingTimer();
      _updateRecordingState(RecordingState.recording);
      
      return true;
    } catch (e) {
      print('Error starting recording: $e');
      return false;
    }
  }

  Future<bool> pauseRecording() async {
    if (_recordingState != RecordingState.recording) return false;

    try {
      await _recorder.pause();
      _stopRecordingTimer();
      _updateRecordingState(RecordingState.paused);
      return true;
    } catch (e) {
      print('Error pausing recording: $e');
      return false;
    }
  }

  Future<bool> resumeRecording() async {
    if (_recordingState != RecordingState.paused) return false;

    try {
      await _recorder.resume();
      _startRecordingTimer();
      _updateRecordingState(RecordingState.recording);
      return true;
    } catch (e) {
      print('Error resuming recording: $e');
      return false;
    }
  }

  Future<String?> stopRecording() async {
    if (_recordingState == RecordingState.idle) return null;

    try {
      final path = await _recorder.stop();
      _stopRecordingTimer();
      _updateRecordingState(RecordingState.stopped);
      
      // Reset to idle after a brief delay
      Future.delayed(const Duration(milliseconds: 500), () {
        _updateRecordingState(RecordingState.idle);
      });

      return path ?? _currentRecordingPath;
    } catch (e) {
      print('Error stopping recording: $e');
      return null;
    }
  }

  Future<bool> playAudio(String filePath) async {
    if (_playbackState == PlaybackState.playing) {
      await stopPlayback();
    }

    try {
      _currentPlayingPath = filePath;
      await _player.play(DeviceFileSource(filePath));
      return true;
    } catch (e) {
      print('Error playing audio: $e');
      return false;
    }
  }

  Future<bool> pausePlayback() async {
    if (_playbackState != PlaybackState.playing) return false;

    try {
      await _player.pause();
      return true;
    } catch (e) {
      print('Error pausing playback: $e');
      return false;
    }
  }

  Future<bool> resumePlayback() async {
    if (_playbackState != PlaybackState.paused) return false;

    try {
      await _player.resume();
      return true;
    } catch (e) {
      print('Error resuming playback: $e');
      return false;
    }
  }

  Future<bool> stopPlayback() async {
    try {
      await _player.stop();
      _currentPlayingPath = null;
      return true;
    } catch (e) {
      print('Error stopping playback: $e');
      return false;
    }
  }

  Future<bool> seekTo(Duration position) async {
    try {
      await _player.seek(position);
      return true;
    } catch (e) {
      print('Error seeking: $e');
      return false;
    }
  }

  Future<String?> transcribeAudio(String filePath) async {
    if (!_speechToText.isAvailable) {
      await _initializeSpeechToText();
    }

    if (!_speechToText.isAvailable) {
      return null;
    }

    try {
      // Note: speech_to_text package doesn't support file transcription directly
      // This would need to be implemented with a cloud service like Google Speech-to-Text
      // For now, return a placeholder
      return 'Transcription feature requires cloud service integration';
    } catch (e) {
      print('Error transcribing audio: $e');
      return null;
    }
  }

  void _startRecordingTimer() {
    _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _recordingDuration = Duration(seconds: timer.tick);
      _recordingDurationController.add(_recordingDuration);
    });
  }

  void _stopRecordingTimer() {
    _recordingTimer?.cancel();
    _recordingTimer = null;
  }

  void _updateRecordingState(RecordingState state) {
    _recordingState = state;
    _recordingStateController.add(state);
  }

  void _updatePlaybackState(PlaybackState state) {
    _playbackState = state;
    _playbackStateController.add(state);
  }

  Future<int> getFileSize(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.length();
      }
      return 0;
    } catch (e) {
      print('Error getting file size: $e');
      return 0;
    }
  }

  Future<bool> deleteAudioFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      print('Error deleting audio file: $e');
      return false;
    }
  }

  void dispose() {
    _recordingTimer?.cancel();
    _playerSubscription?.cancel();
    _recordingStateController.close();
    _playbackStateController.close();
    _recordingDurationController.close();
    _playbackPositionController.close();
    _recorder.dispose();
    _player.dispose();
  }
}
