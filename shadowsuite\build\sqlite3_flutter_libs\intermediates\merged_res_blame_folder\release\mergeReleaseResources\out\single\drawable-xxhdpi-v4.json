[{"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/drawable-xxhdpi-v4/ic_call_answer.png", "source": "eu.simonbinder.sqlite3_flutter_libs-core-1.13.1-7:/drawable-xxhdpi-v4/ic_call_answer.png"}, {"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/drawable-xxhdpi-v4/ic_call_answer_video.png", "source": "eu.simonbinder.sqlite3_flutter_libs-core-1.13.1-7:/drawable-xxhdpi-v4/ic_call_answer_video.png"}, {"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/drawable-xxhdpi-v4/ic_call_decline_low.png", "source": "eu.simonbinder.sqlite3_flutter_libs-core-1.13.1-7:/drawable-xxhdpi-v4/ic_call_decline_low.png"}, {"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/drawable-xxhdpi-v4/ic_call_answer_low.png", "source": "eu.simonbinder.sqlite3_flutter_libs-core-1.13.1-7:/drawable-xxhdpi-v4/ic_call_answer_low.png"}, {"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/drawable-xxhdpi-v4/ic_call_answer_video_low.png", "source": "eu.simonbinder.sqlite3_flutter_libs-core-1.13.1-7:/drawable-xxhdpi-v4/ic_call_answer_video_low.png"}, {"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/drawable-xxhdpi-v4/ic_call_decline.png", "source": "eu.simonbinder.sqlite3_flutter_libs-core-1.13.1-7:/drawable-xxhdpi-v4/ic_call_decline.png"}]