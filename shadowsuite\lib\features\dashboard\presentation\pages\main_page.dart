import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_nav_bar/google_nav_bar.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/providers/app_providers.dart';
import '../../../../shared/theme/app_theme.dart';
import '../../../../flutter_gen/gen_l10n/app_localizations.dart';
import 'dashboard_page.dart';
import '../../../memo_suite/presentation/pages/memo_suite_page.dart';
import '../../../athkar_pro/presentation/pages/athkar_pro_page.dart';
import '../../../money_flow/presentation/pages/money_flow_page.dart';
import '../../../profile/presentation/pages/profile_page.dart';
import '../../../settings/presentation/pages/settings_page.dart';

class MainPage extends ConsumerStatefulWidget {
  const MainPage({super.key});

  @override
  ConsumerState<MainPage> createState() => _MainPageState();
}

class _MainPageState extends ConsumerState<MainPage> {
  int _selectedIndex = 0;
  final PageController _pageController = PageController();

  final List<Widget> _pages = [
    const DashboardPage(),
    const MemoSuitePage(),
    const AthkarProPage(),
    const MoneyFlowPage(),
    const ProfilePage(),
    const SettingsPage(),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final syncStatus = ref.watch(syncStatusProvider);
    final isConnected = ref.watch(connectivityProvider);

    return Scaffold(
      body: Column(
        children: [
          // Sync Status Bar
          if (syncStatus != SyncStatus.idle || !isConnected)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.defaultPadding,
                vertical: 8,
              ),
              color: _getSyncStatusColor(syncStatus, isConnected),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (syncStatus == SyncStatus.syncing)
                    const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    ),
                  if (syncStatus == SyncStatus.syncing)
                    const SizedBox(width: 8),
                  Icon(
                    _getSyncStatusIcon(syncStatus, isConnected),
                    size: 16,
                    color: Colors.white,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _getSyncStatusText(syncStatus, isConnected, l10n),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          // Main Content
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _selectedIndex = index;
                });
              },
              children: _pages,
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          boxShadow: [
            BoxShadow(
              blurRadius: 20,
              color: Colors.black.withOpacity(.1),
            ),
          ],
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.defaultPadding,
              vertical: AppConstants.defaultMargin,
            ),
            child: GNav(
              rippleColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              hoverColor: Theme.of(context).colorScheme.primary.withOpacity(0.05),
              gap: 8,
              activeColor: Theme.of(context).colorScheme.primary,
              iconSize: 24,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              duration: const Duration(milliseconds: 400),
              tabBackgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              tabs: [
                GButton(
                  icon: Icons.dashboard_outlined,
                  text: l10n.dashboard,
                ),
                GButton(
                  icon: Icons.mic_outlined,
                  text: l10n.memoSuite,
                ),
                GButton(
                  icon: Icons.auto_stories_outlined,
                  text: l10n.athkarPro,
                ),
                GButton(
                  icon: Icons.account_balance_wallet_outlined,
                  text: l10n.moneyFlow,
                ),
                GButton(
                  icon: Icons.person_outline,
                  text: l10n.profile,
                ),
                GButton(
                  icon: Icons.settings_outlined,
                  text: l10n.settings,
                ),
              ],
              selectedIndex: _selectedIndex,
              onTabChange: _onItemTapped,
            ),
          ),
        ),
      ),
    );
  }

  Color _getSyncStatusColor(SyncStatus syncStatus, bool isConnected) {
    if (!isConnected) return Colors.red;
    
    switch (syncStatus) {
      case SyncStatus.syncing:
        return Colors.blue;
      case SyncStatus.success:
        return Colors.green;
      case SyncStatus.error:
        return Colors.red;
      case SyncStatus.idle:
        return Colors.transparent;
    }
  }

  IconData _getSyncStatusIcon(SyncStatus syncStatus, bool isConnected) {
    if (!isConnected) return Icons.wifi_off;
    
    switch (syncStatus) {
      case SyncStatus.syncing:
        return Icons.sync;
      case SyncStatus.success:
        return Icons.check_circle;
      case SyncStatus.error:
        return Icons.error;
      case SyncStatus.idle:
        return Icons.wifi;
    }
  }

  String _getSyncStatusText(SyncStatus syncStatus, bool isConnected, AppLocalizations l10n) {
    if (!isConnected) return l10n.offline;
    
    switch (syncStatus) {
      case SyncStatus.syncing:
        return l10n.syncing;
      case SyncStatus.success:
        return l10n.syncComplete;
      case SyncStatus.error:
        return l10n.syncFailed;
      case SyncStatus.idle:
        return l10n.online;
    }
  }
}
