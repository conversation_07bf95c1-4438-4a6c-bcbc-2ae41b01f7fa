import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../core/constants/app_constants.dart';

enum TextFieldType {
  text,
  email,
  password,
  number,
  decimal,
  multiline,
  search,
  url,
  phone,
}

class CustomTextField extends StatefulWidget {
  final String? label;
  final String? hint;
  final String? initialValue;
  final TextFieldType type;
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;
  final VoidCallback? onTap;
  final String? Function(String?)? validator;
  final bool enabled;
  final bool readOnly;
  final bool required;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final VoidCallback? onSuffixIconPressed;
  final int? maxLength;
  final int? maxLines;
  final int? minLines;
  final TextInputAction? textInputAction;
  final FocusNode? focusNode;
  final TextEditingController? controller;
  final bool autofocus;
  final bool obscureText;
  final String? helperText;
  final String? errorText;
  final bool showCounter;
  final List<TextInputFormatter>? inputFormatters;

  const CustomTextField({
    super.key,
    this.label,
    this.hint,
    this.initialValue,
    this.type = TextFieldType.text,
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    this.validator,
    this.enabled = true,
    this.readOnly = false,
    this.required = false,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconPressed,
    this.maxLength,
    this.maxLines,
    this.minLines,
    this.textInputAction,
    this.focusNode,
    this.controller,
    this.autofocus = false,
    this.obscureText = false,
    this.helperText,
    this.errorText,
    this.showCounter = false,
    this.inputFormatters,
  });

  @override
  State<CustomTextField> createState() => _CustomTextFieldState();
}

class _CustomTextFieldState extends State<CustomTextField> {
  late TextEditingController _controller;
  bool _obscureText = false;
  bool _hasFocus = false;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    if (widget.initialValue != null) {
      _controller.text = widget.initialValue!;
    }
    _obscureText = widget.obscureText || widget.type == TextFieldType.password;
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  TextInputType _getKeyboardType() {
    switch (widget.type) {
      case TextFieldType.email:
        return TextInputType.emailAddress;
      case TextFieldType.number:
        return TextInputType.number;
      case TextFieldType.decimal:
        return const TextInputType.numberWithOptions(decimal: true);
      case TextFieldType.multiline:
        return TextInputType.multiline;
      case TextFieldType.url:
        return TextInputType.url;
      case TextFieldType.phone:
        return TextInputType.phone;
      default:
        return TextInputType.text;
    }
  }

  List<TextInputFormatter> _getInputFormatters() {
    final formatters = <TextInputFormatter>[];

    if (widget.inputFormatters != null) {
      formatters.addAll(widget.inputFormatters!);
    }

    switch (widget.type) {
      case TextFieldType.number:
        formatters.add(FilteringTextInputFormatter.digitsOnly);
        break;
      case TextFieldType.decimal:
        formatters.add(FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')));
        break;
      case TextFieldType.phone:
        formatters.add(FilteringTextInputFormatter.allow(RegExp(r'[0-9+\-\s\(\)]')));
        break;
      default:
        break;
    }

    if (widget.maxLength != null) {
      formatters.add(LengthLimitingTextInputFormatter(widget.maxLength));
    }

    return formatters;
  }

  Widget? _buildPrefixIcon() {
    if (widget.prefixIcon != null) {
      return Icon(widget.prefixIcon);
    }

    switch (widget.type) {
      case TextFieldType.email:
        return const Icon(Icons.email_outlined);
      case TextFieldType.password:
        return const Icon(Icons.lock_outlined);
      case TextFieldType.search:
        return const Icon(Icons.search);
      case TextFieldType.url:
        return const Icon(Icons.link);
      case TextFieldType.phone:
        return const Icon(Icons.phone_outlined);
      default:
        return null;
    }
  }

  Widget? _buildSuffixIcon() {
    if (widget.type == TextFieldType.password) {
      return IconButton(
        icon: Icon(_obscureText ? Icons.visibility : Icons.visibility_off),
        onPressed: () {
          setState(() {
            _obscureText = !_obscureText;
          });
        },
      );
    }

    if (widget.suffixIcon != null) {
      return IconButton(
        icon: Icon(widget.suffixIcon),
        onPressed: widget.onSuffixIconPressed,
      );
    }

    if (widget.type == TextFieldType.search && _controller.text.isNotEmpty) {
      return IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          _controller.clear();
          widget.onChanged?.call('');
        },
      );
    }

    return null;
  }

  String? _buildLabelText() {
    if (widget.label == null) return null;
    return widget.required ? '${widget.label} *' : widget.label;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Focus(
          onFocusChange: (hasFocus) {
            setState(() {
              _hasFocus = hasFocus;
            });
          },
          child: TextFormField(
            controller: _controller,
            focusNode: widget.focusNode,
            keyboardType: _getKeyboardType(),
            textInputAction: widget.textInputAction,
            inputFormatters: _getInputFormatters(),
            maxLines: widget.type == TextFieldType.multiline 
                ? (widget.maxLines ?? 3) 
                : (widget.maxLines ?? 1),
            minLines: widget.minLines,
            enabled: widget.enabled,
            readOnly: widget.readOnly,
            autofocus: widget.autofocus,
            obscureText: _obscureText,
            onChanged: widget.onChanged,
            onFieldSubmitted: widget.onSubmitted,
            onTap: widget.onTap,
            validator: widget.validator,
            decoration: InputDecoration(
              labelText: _buildLabelText(),
              hintText: widget.hint,
              helperText: widget.helperText,
              errorText: widget.errorText,
              prefixIcon: _buildPrefixIcon(),
              suffixIcon: _buildSuffixIcon(),
              counterText: widget.showCounter ? null : '',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                borderSide: BorderSide(
                  color: theme.colorScheme.outline,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                borderSide: BorderSide(
                  color: theme.colorScheme.primary,
                  width: 2,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                borderSide: BorderSide(
                  color: theme.colorScheme.error,
                ),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                borderSide: BorderSide(
                  color: theme.colorScheme.error,
                  width: 2,
                ),
              ),
              filled: !widget.enabled,
              fillColor: !widget.enabled 
                  ? theme.colorScheme.surfaceVariant.withOpacity(0.5)
                  : null,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: AppConstants.defaultPadding,
                vertical: AppConstants.defaultMargin,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class CustomSearchField extends StatelessWidget {
  final String? hint;
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;
  final VoidCallback? onClear;
  final TextEditingController? controller;
  final bool autofocus;

  const CustomSearchField({
    super.key,
    this.hint,
    this.onChanged,
    this.onSubmitted,
    this.onClear,
    this.controller,
    this.autofocus = false,
  });

  @override
  Widget build(BuildContext context) {
    return CustomTextField(
      type: TextFieldType.search,
      hint: hint ?? 'Search...',
      onChanged: onChanged,
      onSubmitted: onSubmitted,
      controller: controller,
      autofocus: autofocus,
      textInputAction: TextInputAction.search,
    );
  }
}

class CustomAmountField extends StatelessWidget {
  final String? label;
  final String? hint;
  final String? initialValue;
  final Function(String)? onChanged;
  final String? Function(String?)? validator;
  final bool enabled;
  final bool required;
  final String currency;
  final TextEditingController? controller;

  const CustomAmountField({
    super.key,
    this.label,
    this.hint,
    this.initialValue,
    this.onChanged,
    this.validator,
    this.enabled = true,
    this.required = false,
    this.currency = '\$',
    this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return CustomTextField(
      label: label,
      hint: hint,
      initialValue: initialValue,
      type: TextFieldType.decimal,
      onChanged: onChanged,
      validator: validator,
      enabled: enabled,
      required: required,
      controller: controller,
      prefixIcon: Icons.attach_money,
      textInputAction: TextInputAction.done,
    );
  }
}
