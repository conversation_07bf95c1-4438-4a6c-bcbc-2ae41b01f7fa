name: shadowsuite
description: "ShadowSuite - Production-ready cross-platform app with offline-first SQLite and Supabase sync"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.8.0

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI & Navigation
  cupertino_icons: ^1.0.8
  material_color_utilities: ^0.11.1
  google_nav_bar: ^5.0.6

  # State Management
  flutter_riverpod: ^2.5.1
  riverpod_annotation: ^2.3.5

  # Database & Storage
  sqflite: ^2.3.3+1
  sqflite_common_ffi: ^2.3.3
  sqlite3_flutter_libs: ^0.5.24
  path: ^1.9.0
  path_provider: ^2.1.4

  # Supabase Integration
  supabase_flutter: ^2.5.6

  # Audio & Recording
  record: ^5.1.2
  audioplayers: ^6.1.0
  permission_handler: ^11.3.1
  speech_to_text: ^7.0.0

  # Charts & Visualization
  fl_chart: ^0.69.0

  # Utilities
  intl: ^0.20.2
  uuid: ^4.4.2
  shared_preferences: ^2.3.2
  connectivity_plus: ^6.0.5
  file_picker: ^8.1.2
  share_plus: ^10.0.2
  url_launcher: ^6.3.0
  image_picker: ^1.1.2

  # Notifications
  flutter_local_notifications: ^17.2.3

  # Date & Time
  table_calendar: ^3.1.2

  # JSON & Serialization
  json_annotation: ^4.9.0

  # Encryption
  encrypt: ^5.0.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code Generation
  build_runner: ^2.4.12
  riverpod_generator: ^2.4.3
  json_serializable: ^6.8.0

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/audio/

  fonts:
    - family: Amiri
      fonts:
        - asset: assets/fonts/Amiri-Regular.ttf
        - asset: assets/fonts/Amiri-Bold.ttf
          weight: 700
