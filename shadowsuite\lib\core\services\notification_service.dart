import 'dart:async';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:timezone/timezone.dart' as tz;
import '../constants/app_constants.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _notifications = FlutterLocalNotificationsPlugin();
  bool _isInitialized = false;

  Future<void> initialize() async {
    if (_isInitialized) return;

    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const initSettings = InitializationSettings(android: androidSettings);

    await _notifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    await _createNotificationChannels();
    _isInitialized = true;
  }

  Future<void> _createNotificationChannels() async {
    final androidPlugin = _notifications.resolvePlatformSpecificImplementation<
        AndroidFlutterLocalNotificationsPlugin>();

    if (androidPlugin != null) {
      // Reminder channel for athkar and prayer notifications
      await androidPlugin.createNotificationChannel(
        const AndroidNotificationChannel(
          AppConstants.reminderChannelId,
          'Prayer & Athkar Reminders',
          description: 'Notifications for prayer times and athkar reminders',
          importance: Importance.high,
          enableVibration: true,
          playSound: true,
        ),
      );

      // Sync channel for background sync notifications
      await androidPlugin.createNotificationChannel(
        const AndroidNotificationChannel(
          AppConstants.syncChannelId,
          'Sync Status',
          description: 'Notifications for sync status updates',
          importance: Importance.low,
          enableVibration: false,
          playSound: false,
        ),
      );

      // General channel for other notifications
      await androidPlugin.createNotificationChannel(
        const AndroidNotificationChannel(
          AppConstants.generalChannelId,
          'General Notifications',
          description: 'General app notifications',
          importance: Importance.defaultImportance,
          enableVibration: true,
          playSound: true,
        ),
      );
    }
  }

  Future<bool> requestPermissions() async {
    final status = await Permission.notification.status;
    if (status.isDenied) {
      final result = await Permission.notification.request();
      return result.isGranted;
    }
    return status.isGranted;
  }

  void _onNotificationTapped(NotificationResponse response) {
    final payload = response.payload;
    if (payload != null) {
      // Handle notification tap based on payload
      _handleNotificationPayload(payload);
    }
  }

  void _handleNotificationPayload(String payload) {
    // Parse payload and navigate to appropriate screen
    // This would be implemented based on app navigation structure
    print('Notification tapped with payload: $payload');
  }

  Future<void> showInstantNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
    String channelId = AppConstants.generalChannelId,
  }) async {
    if (!_isInitialized) await initialize();

    const androidDetails = AndroidNotificationDetails(
      AppConstants.generalChannelId,
      'General Notifications',
      channelDescription: 'General app notifications',
      importance: Importance.defaultImportance,
      priority: Priority.defaultPriority,
    );

    const notificationDetails = NotificationDetails(android: androidDetails);

    await _notifications.show(
      id,
      title,
      body,
      notificationDetails,
      payload: payload,
    );
  }

  Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledTime,
    String? payload,
    String channelId = AppConstants.reminderChannelId,
  }) async {
    if (!_isInitialized) await initialize();

    final androidDetails = AndroidNotificationDetails(
      channelId,
      channelId == AppConstants.reminderChannelId 
          ? 'Prayer & Athkar Reminders'
          : 'General Notifications',
      channelDescription: channelId == AppConstants.reminderChannelId
          ? 'Notifications for prayer times and athkar reminders'
          : 'General app notifications',
      importance: channelId == AppConstants.reminderChannelId
          ? Importance.high
          : Importance.defaultImportance,
      priority: Priority.high,
      enableVibration: true,
      playSound: true,
    );

    const notificationDetails = NotificationDetails(android: androidDetails);

    await _notifications.zonedSchedule(
      id,
      title,
      body,
      tz.TZDateTime.from(scheduledTime, tz.local),
      notificationDetails,
      payload: payload,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
      matchDateTimeComponents: DateTimeComponents.time,
    );
  }

  Future<void> scheduleRepeatingNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledTime,
    required RepeatInterval repeatInterval,
    String? payload,
    String channelId = AppConstants.reminderChannelId,
  }) async {
    if (!_isInitialized) await initialize();

    final androidDetails = AndroidNotificationDetails(
      channelId,
      channelId == AppConstants.reminderChannelId 
          ? 'Prayer & Athkar Reminders'
          : 'General Notifications',
      channelDescription: channelId == AppConstants.reminderChannelId
          ? 'Notifications for prayer times and athkar reminders'
          : 'General app notifications',
      importance: channelId == AppConstants.reminderChannelId
          ? Importance.high
          : Importance.defaultImportance,
      priority: Priority.high,
      enableVibration: true,
      playSound: true,
    );

    const notificationDetails = NotificationDetails(android: androidDetails);

    await _notifications.periodicallyShow(
      id,
      title,
      body,
      repeatInterval,
      notificationDetails,
      payload: payload,
    );
  }

  Future<void> schedulePrayerReminders(List<DateTime> prayerTimes) async {
    final prayerNames = AppConstants.prayerNames;
    
    for (int i = 0; i < prayerTimes.length && i < prayerNames.length; i++) {
      final prayerTime = prayerTimes[i];
      final prayerName = prayerNames[i];
      
      // Schedule notification 5 minutes before prayer time
      final reminderTime = prayerTime.subtract(const Duration(minutes: 5));
      
      if (reminderTime.isAfter(DateTime.now())) {
        await scheduleNotification(
          id: 1000 + i, // Unique ID for prayer reminders
          title: 'Prayer Reminder',
          body: '$prayerName prayer time is in 5 minutes',
          scheduledTime: reminderTime,
          payload: 'prayer_reminder_$prayerName',
          channelId: AppConstants.reminderChannelId,
        );
      }
    }
  }

  Future<void> scheduleAthkarReminder({
    required int routineId,
    required String routineName,
    required List<String> reminderTimes,
  }) async {
    for (int i = 0; i < reminderTimes.length; i++) {
      final timeString = reminderTimes[i];
      final timeParts = timeString.split(':');
      
      if (timeParts.length == 2) {
        final hour = int.tryParse(timeParts[0]);
        final minute = int.tryParse(timeParts[1]);
        
        if (hour != null && minute != null) {
          final now = DateTime.now();
          var scheduledTime = DateTime(now.year, now.month, now.day, hour, minute);
          
          // If the time has passed today, schedule for tomorrow
          if (scheduledTime.isBefore(now)) {
            scheduledTime = scheduledTime.add(const Duration(days: 1));
          }
          
          await scheduleRepeatingNotification(
            id: 2000 + routineId + i, // Unique ID for athkar reminders
            title: 'Athkar Reminder',
            body: 'Time for your $routineName routine',
            scheduledTime: scheduledTime,
            repeatInterval: RepeatInterval.daily,
            payload: 'athkar_reminder_$routineId',
            channelId: AppConstants.reminderChannelId,
          );
        }
      }
    }
  }

  Future<void> showSyncNotification({
    required String status,
    bool isError = false,
  }) async {
    await showInstantNotification(
      id: 9999, // Fixed ID for sync notifications
      title: 'Sync Status',
      body: status,
      channelId: AppConstants.syncChannelId,
      payload: 'sync_status',
    );
  }

  Future<void> cancelNotification(int id) async {
    await _notifications.cancel(id);
  }

  Future<void> cancelAllNotifications() async {
    await _notifications.cancelAll();
  }

  Future<void> cancelPrayerReminders() async {
    // Cancel prayer reminder notifications (IDs 1000-1004)
    for (int i = 0; i < 5; i++) {
      await cancelNotification(1000 + i);
    }
  }

  Future<void> cancelAthkarReminders(int routineId) async {
    // Cancel athkar reminder notifications for specific routine
    for (int i = 0; i < 10; i++) { // Assuming max 10 reminder times per routine
      await cancelNotification(2000 + routineId + i);
    }
  }

  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    return await _notifications.pendingNotificationRequests();
  }

  Future<List<ActiveNotification>> getActiveNotifications() async {
    final androidPlugin = _notifications.resolvePlatformSpecificImplementation<
        AndroidFlutterLocalNotificationsPlugin>();
    
    if (androidPlugin != null) {
      return await androidPlugin.getActiveNotifications();
    }
    return [];
  }
}
