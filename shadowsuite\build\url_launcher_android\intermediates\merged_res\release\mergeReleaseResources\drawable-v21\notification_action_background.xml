<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2017 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="@color/androidx_core_ripple_material_light">
    <item android:id="@android:id/mask">
        <inset xmlns:android="http://schemas.android.com/apk/res/android"
               android:insetLeft="@dimen/compat_button_inset_horizontal_material"
               android:insetTop="@dimen/compat_button_inset_vertical_material"
               android:insetRight="@dimen/compat_button_inset_horizontal_material"
               android:insetBottom="@dimen/compat_button_inset_vertical_material">
            <shape android:shape="rectangle">
                <corners android:radius="@dimen/compat_control_corner_material" />
                <solid android:color="@android:color/white" />
                <padding android:left="@dimen/compat_button_padding_horizontal_material"
                         android:top="@dimen/compat_button_padding_vertical_material"
                         android:right="@dimen/compat_button_padding_horizontal_material"
                         android:bottom="@dimen/compat_button_padding_vertical_material" />
            </shape>
        </inset>
    </item>
</ripple>