import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_nav_bar/google_nav_bar.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../shared/theme/app_theme.dart';
import '../../../../l10n/app_localizations.dart';
import '../widgets/routines_tab.dart';
import '../widgets/counter_tab.dart';
import '../widgets/progress_tab.dart';

class AthkarProPage extends ConsumerStatefulWidget {
  const AthkarProPage({super.key});

  @override
  ConsumerState<AthkarProPage> createState() => _AthkarProPageState();
}

class _AthkarProPageState extends ConsumerState<AthkarProPage> {
  int _selectedIndex = 0;
  final PageController _pageController = PageController();

  final List<Widget> _pages = [
    const RoutinesTab(),
    const CounterTab(),
    const ProgressTab(),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.athkarPro),
        centerTitle: true,
        backgroundColor: AppTheme.athkarColor.withOpacity(0.1),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              // Navigate to add routine
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              // Navigate to athkar settings
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _selectedIndex = index;
                });
              },
              children: _pages,
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          boxShadow: [
            BoxShadow(blurRadius: 20, color: Colors.black.withOpacity(.1)),
          ],
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.defaultPadding,
              vertical: AppConstants.defaultMargin,
            ),
            child: GNav(
              rippleColor: AppTheme.athkarColor.withOpacity(0.1),
              hoverColor: AppTheme.athkarColor.withOpacity(0.05),
              gap: 8,
              activeColor: AppTheme.athkarColor,
              iconSize: 24,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              duration: const Duration(milliseconds: 400),
              tabBackgroundColor: AppTheme.athkarColor.withOpacity(0.1),
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              tabs: [
                GButton(icon: Icons.list_outlined, text: l10n.routines),
                GButton(icon: Icons.touch_app_outlined, text: l10n.counter),
                GButton(icon: Icons.trending_up_outlined, text: l10n.progress),
              ],
              selectedIndex: _selectedIndex,
              onTabChange: _onItemTapped,
            ),
          ),
        ),
      ),
    );
  }
}
