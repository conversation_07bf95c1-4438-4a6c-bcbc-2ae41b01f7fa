[{"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/layout-v21/notification_action_tombstone.xml", "source": "eu.simonbinder.sqlite3_flutter_libs-core-1.13.1-7:/layout-v21/notification_action_tombstone.xml"}, {"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/layout-v21/notification_template_icon_group.xml", "source": "eu.simonbinder.sqlite3_flutter_libs-core-1.13.1-7:/layout-v21/notification_template_icon_group.xml"}, {"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/layout-v21/notification_template_custom_big.xml", "source": "eu.simonbinder.sqlite3_flutter_libs-core-1.13.1-7:/layout-v21/notification_template_custom_big.xml"}, {"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/layout-v21/notification_action.xml", "source": "eu.simonbinder.sqlite3_flutter_libs-core-1.13.1-7:/layout-v21/notification_action.xml"}]