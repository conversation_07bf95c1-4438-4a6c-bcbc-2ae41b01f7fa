import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class BudgetsTab extends ConsumerWidget {
  const BudgetsTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.savings_outlined, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text('Budgets Tab', style: TextStyle(fontSize: 24, color: Colors.grey)),
          SizedBox(height: 8),
          Text('Full implementation coming soon', style: TextStyle(color: Colors.grey)),
        ],
      ),
    );
  }
}
