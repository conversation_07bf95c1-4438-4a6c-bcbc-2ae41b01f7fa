import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_nav_bar/google_nav_bar.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../shared/theme/app_theme.dart';
import '../../../../l10n/app_localizations.dart';
import '../widgets/memo_list_tab.dart';
import '../widgets/recording_tab.dart';
import '../widgets/transcription_tab.dart';

class MemoSuitePage extends ConsumerStatefulWidget {
  const MemoSuitePage({super.key});

  @override
  ConsumerState<MemoSuitePage> createState() => _MemoSuitePageState();
}

class _MemoSuitePageState extends ConsumerState<MemoSuitePage> {
  int _selectedIndex = 0;
  final PageController _pageController = PageController();

  final List<Widget> _pages = [
    const MemoListTab(),
    const RecordingTab(),
    const TranscriptionTab(),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.memoSuite),
        centerTitle: true,
        backgroundColor: AppTheme.memoColor.withOpacity(0.1),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // Navigate to search page
            },
          ),
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {
              _showMoreOptions(context);
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _selectedIndex = index;
                });
              },
              children: _pages,
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          boxShadow: [
            BoxShadow(blurRadius: 20, color: Colors.black.withOpacity(.1)),
          ],
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.defaultPadding,
              vertical: AppConstants.defaultMargin,
            ),
            child: GNav(
              rippleColor: AppTheme.memoColor.withOpacity(0.1),
              hoverColor: AppTheme.memoColor.withOpacity(0.05),
              gap: 8,
              activeColor: AppTheme.memoColor,
              iconSize: 24,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              duration: const Duration(milliseconds: 400),
              tabBackgroundColor: AppTheme.memoColor.withOpacity(0.1),
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              tabs: [
                GButton(icon: Icons.list_outlined, text: l10n.memos),
                GButton(icon: Icons.mic_outlined, text: l10n.record),
                GButton(
                  icon: Icons.transcribe_outlined,
                  text: l10n.transcription,
                ),
              ],
              selectedIndex: _selectedIndex,
              onTabChange: _onItemTapped,
            ),
          ),
        ),
      ),
    );
  }

  void _showMoreOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('Settings'),
              onTap: () {
                Navigator.pop(context);
                // Navigate to memo settings
              },
            ),
            ListTile(
              leading: const Icon(Icons.export_notes),
              title: const Text('Export All'),
              onTap: () {
                Navigator.pop(context);
                // Export all memos
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete_sweep),
              title: const Text('Clear All'),
              onTap: () {
                Navigator.pop(context);
                _showClearAllDialog(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showClearAllDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Memos'),
        content: const Text(
          'Are you sure you want to delete all memos? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Clear all memos
            },
            child: const Text('Delete All'),
          ),
        ],
      ),
    );
  }
}
