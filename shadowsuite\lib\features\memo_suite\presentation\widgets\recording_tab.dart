import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/models/memo.dart';
import '../../../../core/services/audio_service.dart';
import '../../../../core/services/database_service.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../../../shared/theme/app_theme.dart';
import '../../../../core/utils/date_utils.dart';
import 'memo_list_tab.dart';

final audioServiceProvider = Provider<AudioService>((ref) => AudioService());

final recordingStateProvider = StreamProvider<RecordingState>((ref) {
  final audioService = ref.watch(audioServiceProvider);
  return audioService.recordingStateStream;
});

final recordingDurationProvider = StreamProvider<Duration>((ref) {
  final audioService = ref.watch(audioServiceProvider);
  return audioService.recordingDurationStream;
});

class RecordingTab extends ConsumerStatefulWidget {
  const RecordingTab({super.key});

  @override
  ConsumerState<RecordingTab> createState() => _RecordingTabState();
}

class _RecordingTabState extends ConsumerState<RecordingTab> {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  String? _currentRecordingPath;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _initializeAudioService();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _initializeAudioService() async {
    final audioService = ref.read(audioServiceProvider);
    await audioService.initialize();
  }

  @override
  Widget build(BuildContext context) {
    final recordingState = ref.watch(recordingStateProvider);
    final recordingDuration = ref.watch(recordingDurationProvider);

    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Recording Controls
            Expanded(
              flex: 2,
              child: _buildRecordingControls(recordingState, recordingDuration),
            ),

            const SizedBox(height: 24),

            // Memo Details Form
            Expanded(flex: 3, child: _buildMemoForm()),
          ],
        ),
      ),
    );
  }

  Widget _buildRecordingControls(
    AsyncValue<RecordingState> recordingStateAsync,
    AsyncValue<Duration> recordingDurationAsync,
  ) {
    return recordingStateAsync.when(
      data: (recordingState) {
        return recordingDurationAsync.when(
          data: (duration) => _buildRecordingUI(recordingState, duration),
          loading: () => _buildRecordingUI(recordingState, Duration.zero),
          error: (_, __) => _buildRecordingUI(recordingState, Duration.zero),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Audio service error',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecordingUI(RecordingState state, Duration duration) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding * 2),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Recording Indicator
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _getRecordingColor(state).withOpacity(0.1),
                border: Border.all(color: _getRecordingColor(state), width: 3),
              ),
              child: Center(
                child: Icon(
                  _getRecordingIcon(state),
                  size: 48,
                  color: _getRecordingColor(state),
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Duration Display
            Text(
              AppDateUtils.formatDuration(duration),
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: _getRecordingColor(state),
              ),
            ),

            const SizedBox(height: 8),

            // Status Text
            Text(
              _getStatusText(state),
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),

            const SizedBox(height: 32),

            // Control Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: _buildControlButtons(state),
            ),
          ],
        ),
      ),
    );
  }

  Color _getRecordingColor(RecordingState state) {
    switch (state) {
      case RecordingState.recording:
        return Colors.red;
      case RecordingState.paused:
        return Colors.orange;
      case RecordingState.stopped:
        return Colors.green;
      case RecordingState.idle:
      default:
        return AppTheme.memoColor;
    }
  }

  IconData _getRecordingIcon(RecordingState state) {
    switch (state) {
      case RecordingState.recording:
        return Icons.mic;
      case RecordingState.paused:
        return Icons.pause;
      case RecordingState.stopped:
        return Icons.stop;
      case RecordingState.idle:
      default:
        return Icons.mic_none;
    }
  }

  String _getStatusText(RecordingState state) {
    switch (state) {
      case RecordingState.recording:
        return 'Recording...';
      case RecordingState.paused:
        return 'Paused';
      case RecordingState.stopped:
        return 'Recording complete';
      case RecordingState.idle:
      default:
        return 'Ready to record';
    }
  }

  List<Widget> _buildControlButtons(RecordingState state) {
    final audioService = ref.read(audioServiceProvider);

    switch (state) {
      case RecordingState.idle:
        return [
          CustomButton(
            text: 'Start Recording',
            icon: Icons.mic,
            type: ButtonType.primary,
            customColor: Colors.red,
            onPressed: () async {
              final success = await audioService.startRecording();
              if (success) {
                _currentRecordingPath = audioService.currentRecordingPath;
              } else {
                _showErrorSnackBar('Failed to start recording');
              }
            },
          ),
        ];

      case RecordingState.recording:
        return [
          CustomButton(
            text: 'Pause',
            icon: Icons.pause,
            type: ButtonType.outlined,
            onPressed: () async {
              await audioService.pauseRecording();
            },
          ),
          const SizedBox(width: 16),
          CustomButton(
            text: 'Stop',
            icon: Icons.stop,
            type: ButtonType.primary,
            customColor: Colors.green,
            onPressed: () async {
              final path = await audioService.stopRecording();
              if (path != null) {
                _currentRecordingPath = path;
              }
            },
          ),
        ];

      case RecordingState.paused:
        return [
          CustomButton(
            text: 'Resume',
            icon: Icons.play_arrow,
            type: ButtonType.primary,
            customColor: Colors.red,
            onPressed: () async {
              await audioService.resumeRecording();
            },
          ),
          const SizedBox(width: 16),
          CustomButton(
            text: 'Stop',
            icon: Icons.stop,
            type: ButtonType.outlined,
            onPressed: () async {
              final path = await audioService.stopRecording();
              if (path != null) {
                _currentRecordingPath = path;
              }
            },
          ),
        ];

      case RecordingState.stopped:
        return [
          CustomButton(
            text: 'New Recording',
            icon: Icons.mic,
            type: ButtonType.outlined,
            onPressed: () async {
              _currentRecordingPath = null;
              final success = await audioService.startRecording();
              if (success) {
                _currentRecordingPath = audioService.currentRecordingPath;
              }
            },
          ),
          const SizedBox(width: 16),
          CustomButton(
            text: 'Play',
            icon: Icons.play_arrow,
            type: ButtonType.primary,
            onPressed: _currentRecordingPath != null
                ? () async {
                    await audioService.playAudio(_currentRecordingPath!);
                  }
                : null,
          ),
        ];
    }
  }

  Widget _buildMemoForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text('Memo Details', style: Theme.of(context).textTheme.titleLarge),

        const SizedBox(height: 16),

        CustomTextField(
          controller: _titleController,
          label: 'Title',
          hint: 'Enter memo title',
          required: true,
          maxLength: AppConstants.maxMemoTitleLength,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Title is required';
            }
            return null;
          },
        ),

        const SizedBox(height: 16),

        CustomTextField(
          controller: _descriptionController,
          label: 'Description',
          hint: 'Enter memo description (optional)',
          type: TextFieldType.multiline,
          maxLines: 3,
          maxLength: AppConstants.maxMemoDescriptionLength,
        ),

        const Spacer(),

        CustomButton(
          text: 'Save Memo',
          icon: Icons.save,
          type: ButtonType.primary,
          isFullWidth: true,
          isLoading: _isSaving,
          onPressed: _canSaveMemo() ? _saveMemo : null,
        ),
      ],
    );
  }

  bool _canSaveMemo() {
    return _currentRecordingPath != null &&
        _titleController.text.trim().isNotEmpty &&
        !_isSaving;
  }

  Future<void> _saveMemo() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSaving = true;
    });

    try {
      final audioService = ref.read(audioServiceProvider);
      final memoService = MemoDatabaseService();

      // Get file size
      final fileSize = _currentRecordingPath != null
          ? await audioService.getFileSize(_currentRecordingPath!)
          : 0;

      // Get duration
      final duration =
          ref.read(recordingDurationProvider).value ?? Duration.zero;

      // Create memo
      final memo = Memo(
        id: const Uuid().v4(),
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        audioPath: _currentRecordingPath,
        duration: duration.inSeconds,
        fileSize: fileSize,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Save to database
      await memoService.insert(memo);

      // Clear form
      _titleController.clear();
      _descriptionController.clear();
      _currentRecordingPath = null;

      // Show success message
      _showSuccessSnackBar('Memo saved successfully');

      // Refresh memo list
      ref.invalidate(memoListProvider);
    } catch (e) {
      _showErrorSnackBar('Failed to save memo: $e');
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }
}
