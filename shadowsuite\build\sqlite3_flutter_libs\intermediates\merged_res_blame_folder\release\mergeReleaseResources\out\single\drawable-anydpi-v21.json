[{"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/drawable-anydpi-v21/ic_call_answer_low.xml", "source": "eu.simonbinder.sqlite3_flutter_libs-core-1.13.1-7:/drawable-anydpi-v21/ic_call_answer_low.xml"}, {"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/drawable-anydpi-v21/ic_call_answer_video.xml", "source": "eu.simonbinder.sqlite3_flutter_libs-core-1.13.1-7:/drawable-anydpi-v21/ic_call_answer_video.xml"}, {"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/drawable-anydpi-v21/ic_call_answer_video_low.xml", "source": "eu.simonbinder.sqlite3_flutter_libs-core-1.13.1-7:/drawable-anydpi-v21/ic_call_answer_video_low.xml"}, {"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/drawable-anydpi-v21/ic_call_decline.xml", "source": "eu.simonbinder.sqlite3_flutter_libs-core-1.13.1-7:/drawable-anydpi-v21/ic_call_decline.xml"}, {"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/drawable-anydpi-v21/ic_call_decline_low.xml", "source": "eu.simonbinder.sqlite3_flutter_libs-core-1.13.1-7:/drawable-anydpi-v21/ic_call_decline_low.xml"}, {"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/drawable-anydpi-v21/ic_call_answer.xml", "source": "eu.simonbinder.sqlite3_flutter_libs-core-1.13.1-7:/drawable-anydpi-v21/ic_call_answer.xml"}]