import 'dart:async';
import 'dart:convert';
import 'package:supabase_flutter/supabase_flutter.dart' hide User;
import 'package:connectivity_plus/connectivity_plus.dart';
import '../constants/app_constants.dart';
import '../models/user.dart';
import '../models/memo.dart';
import '../models/athkar_routine.dart';
import '../models/money_flow.dart' as models;
import 'database_service.dart';

enum SyncStatus { idle, syncing, success, error }

class SyncService {
  static final SyncService _instance = SyncService._internal();
  factory SyncService() => _instance;
  SyncService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  final Connectivity _connectivity = Connectivity();

  // Database services
  final UserDatabaseService _userDb = UserDatabaseService();
  final MemoDatabaseService _memoDb = MemoDatabaseService();
  final AthkarDatabaseService _athkarDb = AthkarDatabaseService();
  final AccountDatabaseService _accountDb = AccountDatabaseService();
  final CategoryDatabaseService _categoryDb = CategoryDatabaseService();
  final TransactionDatabaseService _transactionDb =
      TransactionDatabaseService();

  SyncStatus _syncStatus = SyncStatus.idle;
  String? _lastError;
  DateTime? _lastSyncTime;
  Timer? _autoSyncTimer;

  // Stream controllers
  final StreamController<SyncStatus> _syncStatusController =
      StreamController<SyncStatus>.broadcast();
  final StreamController<String?> _syncErrorController =
      StreamController<String?>.broadcast();

  // Getters
  SyncStatus get syncStatus => _syncStatus;
  String? get lastError => _lastError;
  DateTime? get lastSyncTime => _lastSyncTime;

  // Streams
  Stream<SyncStatus> get syncStatusStream => _syncStatusController.stream;
  Stream<String?> get syncErrorStream => _syncErrorController.stream;

  Future<void> initialize() async {
    await _checkConnectivity();
    _startAutoSync();
  }

  Future<bool> _checkConnectivity() async {
    final connectivityResult = await _connectivity.checkConnectivity();
    return !connectivityResult.contains(ConnectivityResult.none);
  }

  void _startAutoSync() {
    _autoSyncTimer = Timer.periodic(
      Duration(minutes: AppConstants.syncIntervalMinutes),
      (_) => syncAll(),
    );
  }

  void _updateSyncStatus(SyncStatus status, [String? error]) {
    _syncStatus = status;
    _lastError = error;
    _syncStatusController.add(status);
    if (error != null) {
      _syncErrorController.add(error);
    }
  }

  Future<bool> syncAll() async {
    if (_syncStatus == SyncStatus.syncing) return false;

    final isConnected = await _checkConnectivity();
    if (!isConnected) {
      _updateSyncStatus(SyncStatus.error, 'No internet connection');
      return false;
    }

    _updateSyncStatus(SyncStatus.syncing);

    try {
      // Get current user
      final user = _supabase.auth.currentUser;
      if (user == null) {
        _updateSyncStatus(SyncStatus.error, 'User not authenticated');
        return false;
      }

      // Sync in order: Users -> Categories -> Accounts -> Memos -> Athkar -> Transactions
      await _syncUsers();
      await _syncCategories(user.id);
      await _syncAccounts(user.id);
      await _syncMemos(user.id);
      await _syncAthkarRoutines(user.id);
      await _syncTransactions(user.id);

      _lastSyncTime = DateTime.now();
      _updateSyncStatus(SyncStatus.success);
      return true;
    } catch (e) {
      _updateSyncStatus(SyncStatus.error, e.toString());
      return false;
    }
  }

  Future<void> _syncUsers() async {
    try {
      // Get local users that need syncing
      final localUsers = await _userDb.getAll();
      final unsyncedUsers = localUsers.where((user) => !user.isSynced).toList();

      for (final user in unsyncedUsers) {
        // Upload to Supabase
        await _supabase.from('users').upsert(user.toJson());

        // Mark as synced locally
        await _userDb.update(user.copyWith(isSynced: true));
      }

      // Download from Supabase
      final remoteUsers = await _supabase.from('users').select();
      for (final userData in remoteUsers) {
        final user = User.fromJson(userData);
        final existingUser = await _userDb.getById(user.id);

        if (existingUser == null) {
          await _userDb.insert(user.copyWith(isSynced: true));
        } else if (user.updatedAt.isAfter(existingUser.updatedAt)) {
          await _userDb.update(user.copyWith(isSynced: true));
        }
      }
    } catch (e) {
      print('Error syncing users: $e');
      rethrow;
    }
  }

  Future<void> _syncMemos(String userId) async {
    try {
      // Get local memos that need syncing
      final localMemos = await _memoDb.getAll(userId: userId);
      final unsyncedMemos = localMemos.where((memo) => !memo.isSynced).toList();

      for (final memo in unsyncedMemos) {
        // Upload to Supabase
        await _supabase.from('memos').upsert(memo.toJson());

        // Mark as synced locally
        await _memoDb.update(memo.copyWith(isSynced: true));
      }

      // Download from Supabase
      final remoteMemos = await _supabase
          .from('memos')
          .select()
          .eq('user_id', userId);

      for (final memoData in remoteMemos) {
        final memo = Memo.fromJson(memoData);
        final existingMemo = await _memoDb.getById(memo.id);

        if (existingMemo == null) {
          await _memoDb.insert(memo.copyWith(isSynced: true));
        } else if (memo.updatedAt.isAfter(existingMemo.updatedAt)) {
          await _memoDb.update(memo.copyWith(isSynced: true));
        }
      }
    } catch (e) {
      print('Error syncing memos: $e');
      rethrow;
    }
  }

  Future<void> _syncAthkarRoutines(String userId) async {
    try {
      // Get local routines that need syncing
      final localRoutines = await _athkarDb.getAll(userId: userId);
      final unsyncedRoutines = localRoutines
          .where((routine) => !routine.isSynced)
          .toList();

      for (final routine in unsyncedRoutines) {
        // Upload to Supabase
        await _supabase.from('athkar_routines').upsert(routine.toJson());

        // Sync athkar items for this routine
        final items = await _athkarDb.getAthkarItems(routine.id);
        for (final item in items) {
          if (!item.isSynced) {
            await _supabase.from('athkar_items').upsert(item.toJson());
            await _athkarDb.updateAthkarItem(item.copyWith(isSynced: true));
          }
        }

        // Mark routine as synced locally
        await _athkarDb.update(routine.copyWith(isSynced: true));
      }

      // Download from Supabase
      final remoteRoutines = await _supabase
          .from('athkar_routines')
          .select()
          .eq('user_id', userId);

      for (final routineData in remoteRoutines) {
        final routine = AthkarRoutine.fromJson(routineData);
        final existingRoutine = await _athkarDb.getById(routine.id);

        if (existingRoutine == null) {
          await _athkarDb.insert(routine.copyWith(isSynced: true));
        } else if (routine.updatedAt.isAfter(existingRoutine.updatedAt)) {
          await _athkarDb.update(routine.copyWith(isSynced: true));
        }

        // Sync athkar items
        final remoteItems = await _supabase
            .from('athkar_items')
            .select()
            .eq('routine_id', routine.id);

        for (final itemData in remoteItems) {
          final item = AthkarItem.fromJson(itemData);
          final localItems = await _athkarDb.getAthkarItems(routine.id);
          final existingItem = localItems
              .where((i) => i.id == item.id)
              .firstOrNull;

          if (existingItem == null) {
            await _athkarDb.insertAthkarItem(item.copyWith(isSynced: true));
          } else if (item.updatedAt.isAfter(existingItem.updatedAt)) {
            await _athkarDb.updateAthkarItem(item.copyWith(isSynced: true));
          }
        }
      }
    } catch (e) {
      print('Error syncing athkar routines: $e');
      rethrow;
    }
  }

  Future<void> _syncAccounts(String userId) async {
    try {
      final localAccounts = await _accountDb.getAll(userId: userId);
      final unsyncedAccounts = localAccounts
          .where((account) => !account.isSynced)
          .toList();

      for (final account in unsyncedAccounts) {
        await _supabase.from('accounts').upsert(account.toJson());
        await _accountDb.update(account.copyWith(isSynced: true));
      }

      final remoteAccounts = await _supabase
          .from('accounts')
          .select()
          .eq('user_id', userId);

      for (final accountData in remoteAccounts) {
        final account = models.Account.fromJson(accountData);
        final existingAccount = await _accountDb.getById(account.id);

        if (existingAccount == null) {
          await _accountDb.insert(account.copyWith(isSynced: true));
        } else if (account.updatedAt.isAfter(existingAccount.updatedAt)) {
          await _accountDb.update(account.copyWith(isSynced: true));
        }
      }
    } catch (e) {
      print('Error syncing accounts: $e');
      rethrow;
    }
  }

  Future<void> _syncCategories(String userId) async {
    try {
      final localCategories = await _categoryDb.getAll(userId: userId);
      final unsyncedCategories = localCategories
          .where((category) => !category.isSynced)
          .toList();

      for (final category in unsyncedCategories) {
        await _supabase.from('categories').upsert(category.toJson());
        await _categoryDb.update(category.copyWith(isSynced: true));
      }

      final remoteCategories = await _supabase
          .from('categories')
          .select()
          .eq('user_id', userId);

      for (final categoryData in remoteCategories) {
        final category = models.Category.fromJson(categoryData);
        final existingCategory = await _categoryDb.getById(category.id);

        if (existingCategory == null) {
          await _categoryDb.insert(category.copyWith(isSynced: true));
        } else if (category.updatedAt.isAfter(existingCategory.updatedAt)) {
          await _categoryDb.update(category.copyWith(isSynced: true));
        }
      }
    } catch (e) {
      print('Error syncing categories: $e');
      rethrow;
    }
  }

  Future<void> _syncTransactions(String userId) async {
    try {
      final localTransactions = await _transactionDb.getAll(userId: userId);
      final unsyncedTransactions = localTransactions
          .where((transaction) => !transaction.isSynced)
          .toList();

      for (final transaction in unsyncedTransactions) {
        await _supabase.from('transactions').upsert(transaction.toJson());
        await _transactionDb.update(transaction.copyWith(isSynced: true));
      }

      final remoteTransactions = await _supabase
          .from('transactions')
          .select()
          .eq('user_id', userId);

      for (final transactionData in remoteTransactions) {
        final transaction = models.Transaction.fromJson(transactionData);
        final existingTransaction = await _transactionDb.getById(
          transaction.id,
        );

        if (existingTransaction == null) {
          await _transactionDb.insert(transaction.copyWith(isSynced: true));
        } else if (transaction.updatedAt.isAfter(
          existingTransaction.updatedAt,
        )) {
          await _transactionDb.update(transaction.copyWith(isSynced: true));
        }
      }
    } catch (e) {
      print('Error syncing transactions: $e');
      rethrow;
    }
  }

  Future<bool> signIn(String email, String password) async {
    try {
      final response = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user != null) {
        await syncAll();
        return true;
      }
      return false;
    } catch (e) {
      _updateSyncStatus(SyncStatus.error, e.toString());
      return false;
    }
  }

  Future<bool> signUp(String email, String password, String name) async {
    try {
      final response = await _supabase.auth.signUp(
        email: email,
        password: password,
      );

      if (response.user != null) {
        // Create user record
        final user = User(
          id: response.user!.id,
          email: email,
          name: name,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await _userDb.insert(user);
        await syncAll();
        return true;
      }
      return false;
    } catch (e) {
      _updateSyncStatus(SyncStatus.error, e.toString());
      return false;
    }
  }

  Future<void> signOut() async {
    try {
      await _supabase.auth.signOut();
      _lastSyncTime = null;
      _updateSyncStatus(SyncStatus.idle);
    } catch (e) {
      print('Error signing out: $e');
    }
  }

  void dispose() {
    _autoSyncTimer?.cancel();
    _syncStatusController.close();
    _syncErrorController.close();
  }
}
