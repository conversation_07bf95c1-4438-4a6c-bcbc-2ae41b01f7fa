import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/models/money_flow.dart' as models;
import '../../../../core/services/database_service.dart';
import '../../../../shared/theme/app_theme.dart';
import '../../../../l10n/app_localizations.dart';

final accountsProvider = FutureProvider<List<models.Account>>((ref) async {
  final accountService = AccountDatabaseService();
  return await accountService.getAll();
});

final totalBalanceProvider = FutureProvider<double>((ref) async {
  final accountService = AccountDatabaseService();
  return await accountService.getTotalBalance();
});

final recentTransactionsProvider = FutureProvider<List<models.Transaction>>((ref) async {
  final transactionService = TransactionDatabaseService();
  final allTransactions = await transactionService.getAll();
  return allTransactions.take(5).toList();
});

final monthlyTotalsProvider = FutureProvider<Map<String, double>>((ref) async {
  final transactionService = TransactionDatabaseService();
  return await transactionService.getMonthlyTotals();
});

class MoneyFlowDashboardTab extends ConsumerWidget {
  const MoneyFlowDashboardTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;
    final totalBalance = ref.watch(totalBalanceProvider);
    final accounts = ref.watch(accountsProvider);
    final recentTransactions = ref.watch(recentTransactionsProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Balance Overview
          _buildBalanceOverview(context, totalBalance, l10n),
          
          const SizedBox(height: 24),
          
          // Quick Stats
          _buildQuickStats(context, l10n),
          
          const SizedBox(height: 24),
          
          // Accounts Summary
          Text(
            l10n.accounts,
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          _buildAccountsSummary(context, accounts, l10n),
          
          const SizedBox(height: 24),
          
          // Recent Transactions
          Text(
            'Recent ${l10n.transactions}',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          _buildRecentTransactions(context, recentTransactions, l10n),
          
          const SizedBox(height: 24),
          
          // Monthly Chart
          Text(
            'Monthly Overview',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          _buildMonthlyChart(context, ref, l10n),
        ],
      ),
    );
  }

  Widget _buildBalanceOverview(BuildContext context, AsyncValue<double> totalBalance, AppLocalizations l10n) {
    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppConstants.defaultPadding * 1.5),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          gradient: AppTheme.primaryGradient,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.total + ' ' + l10n.balance,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.white.withOpacity(0.9),
              ),
            ),
            const SizedBox(height: 8),
            totalBalance.when(
              data: (balance) => Text(
                '\$${balance.toStringAsFixed(2)}',
                style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              loading: () => const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
              error: (error, stack) => Text(
                'Error loading balance',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.white.withOpacity(0.8),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats(BuildContext context, AppLocalizations l10n) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            context,
            title: 'This Month',
            subtitle: l10n.income,
            value: '\$0.00',
            icon: Icons.trending_up,
            color: AppTheme.incomeColor,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            context,
            title: 'This Month',
            subtitle: l10n.expense,
            value: '\$0.00',
            icon: Icons.trending_down,
            color: AppTheme.expenseColor,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountsSummary(BuildContext context, AsyncValue<List<models.Account>> accounts, AppLocalizations l10n) {
    return accounts.when(
      data: (accountList) {
        if (accountList.isEmpty) {
          return Card(
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding * 2),
              child: Column(
                children: [
                  Icon(
                    Icons.account_balance_outlined,
                    size: 48,
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No accounts yet',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Add your first account to get started',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        }

        return Column(
          children: accountList.take(3).map((account) => Card(
            margin: const EdgeInsets.only(bottom: 8),
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: AppTheme.primaryColor.withOpacity(0.2),
                child: Icon(
                  _getAccountIcon(account.type),
                  color: AppTheme.primaryColor,
                ),
              ),
              title: Text(account.name),
              subtitle: Text(account.type),
              trailing: Text(
                account.formattedBalance,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: account.balance >= 0 ? AppTheme.incomeColor : AppTheme.expenseColor,
                ),
              ),
            ),
          )).toList(),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Card(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Text('Error loading accounts: $error'),
        ),
      ),
    );
  }

  Widget _buildRecentTransactions(BuildContext context, AsyncValue<List<models.Transaction>> transactions, AppLocalizations l10n) {
    return transactions.when(
      data: (transactionList) {
        if (transactionList.isEmpty) {
          return Card(
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding * 2),
              child: Column(
                children: [
                  Icon(
                    Icons.receipt_long_outlined,
                    size: 48,
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No transactions yet',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        return Column(
          children: transactionList.map((transaction) => Card(
            margin: const EdgeInsets.only(bottom: 8),
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: transaction.isIncome 
                    ? AppTheme.incomeColor.withOpacity(0.2)
                    : AppTheme.expenseColor.withOpacity(0.2),
                child: Icon(
                  transaction.isIncome ? Icons.add : Icons.remove,
                  color: transaction.isIncome ? AppTheme.incomeColor : AppTheme.expenseColor,
                ),
              ),
              title: Text(transaction.description ?? 'Transaction'),
              subtitle: Text(transaction.date.toString().split(' ')[0]),
              trailing: Text(
                '${transaction.isIncome ? '+' : '-'}${transaction.formattedAmount}',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: transaction.isIncome ? AppTheme.incomeColor : AppTheme.expenseColor,
                ),
              ),
            ),
          )).toList(),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Card(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Text('Error loading transactions: $error'),
        ),
      ),
    );
  }

  Widget _buildMonthlyChart(BuildContext context, WidgetRef ref, AppLocalizations l10n) {
    final monthlyTotals = ref.watch(monthlyTotalsProvider);

    return monthlyTotals.when(
      data: (totals) => Card(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: SizedBox(
            height: 200,
            child: LineChart(
              LineChartData(
                gridData: FlGridData(show: false),
                titlesData: FlTitlesData(show: false),
                borderData: FlBorderData(show: false),
                lineBarsData: [
                  LineChartBarData(
                    spots: _generateChartSpots(totals, 'income'),
                    isCurved: true,
                    color: AppTheme.incomeColor,
                    barWidth: 3,
                    dotData: FlDotData(show: false),
                  ),
                  LineChartBarData(
                    spots: _generateChartSpots(totals, 'expense'),
                    isCurved: true,
                    color: AppTheme.expenseColor,
                    barWidth: 3,
                    dotData: FlDotData(show: false),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      loading: () => const Card(
        child: SizedBox(
          height: 200,
          child: Center(child: CircularProgressIndicator()),
        ),
      ),
      error: (error, stack) => Card(
        child: SizedBox(
          height: 200,
          child: Center(
            child: Text('Error loading chart data'),
          ),
        ),
      ),
    );
  }

  List<FlSpot> _generateChartSpots(Map<String, double> totals, String type) {
    final spots = <FlSpot>[];
    for (int i = 1; i <= 12; i++) {
      final key = '${i.toString().padLeft(2, '0')}_$type';
      final value = totals[key] ?? 0.0;
      spots.add(FlSpot(i.toDouble(), value));
    }
    return spots;
  }

  IconData _getAccountIcon(String type) {
    switch (type.toLowerCase()) {
      case 'checking':
        return Icons.account_balance;
      case 'savings':
        return Icons.savings;
      case 'credit card':
        return Icons.credit_card;
      case 'cash':
        return Icons.money;
      case 'investment':
        return Icons.trending_up;
      case 'loan':
        return Icons.account_balance_wallet;
      default:
        return Icons.account_balance;
    }
  }
}
