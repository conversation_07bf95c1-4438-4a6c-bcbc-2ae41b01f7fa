// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AthkarRoutine _$AthkarRoutineFromJson(Map<String, dynamic> json) =>
    AthkarRoutine(
      id: json['id'] as String,
      userId: json['userId'] as String?,
      title: json['title'] as String,
      description: json['description'] as String?,
      targetCount: (json['targetCount'] as num?)?.toInt() ?? 1,
      currentCount: (json['currentCount'] as num?)?.toInt() ?? 0,
      isCompleted: json['isCompleted'] as bool? ?? false,
      reminderEnabled: json['reminderEnabled'] as bool? ?? false,
      reminderTimes: (json['reminderTimes'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isSynced: json['isSynced'] as bool? ?? false,
    );

Map<String, dynamic> _$AthkarRoutineToJson(AthkarRoutine instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'title': instance.title,
      'description': instance.description,
      'targetCount': instance.targetCount,
      'currentCount': instance.currentCount,
      'isCompleted': instance.isCompleted,
      'reminderEnabled': instance.reminderEnabled,
      'reminderTimes': instance.reminderTimes,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isSynced': instance.isSynced,
    };

AthkarItem _$AthkarItemFromJson(Map<String, dynamic> json) => AthkarItem(
  id: json['id'] as String,
  routineId: json['routineId'] as String,
  arabicText: json['arabicText'] as String,
  transliteration: json['transliteration'] as String?,
  translation: json['translation'] as String?,
  count: (json['count'] as num?)?.toInt() ?? 1,
  orderIndex: (json['orderIndex'] as num?)?.toInt() ?? 0,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  isSynced: json['isSynced'] as bool? ?? false,
);

Map<String, dynamic> _$AthkarItemToJson(AthkarItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'routineId': instance.routineId,
      'arabicText': instance.arabicText,
      'transliteration': instance.transliteration,
      'translation': instance.translation,
      'count': instance.count,
      'orderIndex': instance.orderIndex,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isSynced': instance.isSynced,
    };
