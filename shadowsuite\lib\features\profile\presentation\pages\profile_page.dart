import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/services/auth_service.dart';
import '../../../../core/services/supabase_service.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../../../shared/theme/app_theme.dart';
import '../../../../l10n/app_localizations.dart';
import '../widgets/profile_avatar.dart';
import '../widgets/sync_status_card.dart';
import '../widgets/user_statistics_card.dart';
import '../widgets/data_management_card.dart';

final authServiceProvider = Provider<AuthService>((ref) => AuthService());
final supabaseServiceProvider = Provider<SupabaseService>(
  (ref) => SupabaseService(),
);

final userProvider = StreamProvider((ref) {
  final authService = ref.watch(authServiceProvider);
  return authService.userStream;
});

final profileSyncStatusProvider = StreamProvider((ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  return supabaseService.syncStatusStream;
});

class ProfilePage extends ConsumerStatefulWidget {
  const ProfilePage({super.key});

  @override
  ConsumerState<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends ConsumerState<ProfilePage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  bool _isEditing = false;
  bool _isLoading = false;
  bool _isChangingPassword = false;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final user = ref.watch(userProvider);
    final syncStatus = ref.watch(profileSyncStatusProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.profile),
        centerTitle: true,
        actions: [
          if (!_isEditing)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => _startEditing(),
            ),
          if (_isEditing)
            IconButton(
              icon: const Icon(Icons.close),
              onPressed: () => _cancelEditing(),
            ),
        ],
      ),
      body: user.when(
        data: (userData) {
          if (userData == null) {
            return const Center(child: Text('No user data available'));
          }

          // Update controllers when user data changes
          if (!_isEditing) {
            _nameController.text = userData.name ?? '';
            _emailController.text = userData.email ?? '';
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Profile Avatar Section
                  ProfileAvatar(
                    user: userData,
                    isEditing: _isEditing,
                    onImageSelected: _onImageSelected,
                  ),

                  const SizedBox(height: 32),

                  // Profile Information
                  _buildProfileInformation(l10n, userData),

                  const SizedBox(height: 24),

                  // Sync Status
                  SyncStatusCard(syncStatus: syncStatus),

                  const SizedBox(height: 24),

                  // User Statistics
                  UserStatisticsCard(userId: userData.id),

                  const SizedBox(height: 24),

                  // Data Management
                  DataManagementCard(userId: userData.id),

                  const SizedBox(height: 24),

                  // Password Change Section
                  if (_isChangingPassword) _buildPasswordChangeSection(l10n),

                  const SizedBox(height: 24),

                  // Action Buttons
                  _buildActionButtons(l10n),
                ],
              ),
            ),
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                'Error loading profile',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileInformation(AppLocalizations l10n, user) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.personalInformation,
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),

            CustomTextField(
              controller: _nameController,
              label: l10n.name,
              enabled: _isEditing,
              required: true,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Name is required';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            CustomTextField(
              controller: _emailController,
              label: l10n.email,
              type: TextFieldType.email,
              enabled: _isEditing,
              required: true,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Email is required';
                }
                if (!RegExp(
                  r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                ).hasMatch(value)) {
                  return 'Please enter a valid email';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                Text(
                  'Member since: ',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
                Text(
                  '${user.createdAt.day}/${user.createdAt.month}/${user.createdAt.year}',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPasswordChangeSection(AppLocalizations l10n) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.changePassword,
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),

            CustomTextField(
              controller: _currentPasswordController,
              label: l10n.currentPassword,
              type: TextFieldType.password,
              required: true,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Current password is required';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            CustomTextField(
              controller: _newPasswordController,
              label: l10n.newPassword,
              type: TextFieldType.password,
              required: true,
              validator: (value) {
                if (value == null || value.length < 8) {
                  return 'Password must be at least 8 characters';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            CustomTextField(
              controller: _confirmPasswordController,
              label: l10n.confirmPassword,
              type: TextFieldType.password,
              required: true,
              validator: (value) {
                if (value != _newPasswordController.text) {
                  return 'Passwords do not match';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: CustomButton(
                    text: l10n.updatePassword,
                    type: ButtonType.primary,
                    isLoading: _isLoading,
                    onPressed: _updatePassword,
                  ),
                ),
                const SizedBox(width: 12),
                CustomButton(
                  text: l10n.cancel,
                  type: ButtonType.outlined,
                  onPressed: () {
                    setState(() {
                      _isChangingPassword = false;
                    });
                    _clearPasswordFields();
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        if (_isEditing) ...[
          CustomButton(
            text: l10n.saveChanges,
            type: ButtonType.primary,
            isLoading: _isLoading,
            onPressed: _saveProfile,
          ),
          const SizedBox(height: 12),
          CustomButton(
            text: l10n.cancel,
            type: ButtonType.outlined,
            onPressed: _cancelEditing,
          ),
        ] else ...[
          if (!_isChangingPassword)
            CustomButton(
              text: l10n.changePassword,
              type: ButtonType.outlined,
              onPressed: () {
                setState(() {
                  _isChangingPassword = true;
                });
              },
            ),
          const SizedBox(height: 12),
          CustomButton(
            text: l10n.signOut,
            type: ButtonType.danger,
            onPressed: _signOut,
          ),
        ],
      ],
    );
  }

  void _startEditing() {
    setState(() {
      _isEditing = true;
    });
  }

  void _cancelEditing() {
    setState(() {
      _isEditing = false;
    });

    // Reset form fields
    final user = ref.read(userProvider).value;
    if (user != null) {
      _nameController.text = user.name ?? '';
      _emailController.text = user.email ?? '';
    }
  }

  Future<void> _onImageSelected() async {
    // Image selection would be implemented here
    // For now, just show a placeholder
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Image selection coming soon')),
    );
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final authService = ref.read(authServiceProvider);

      final success = await authService.updateProfile(
        name: _nameController.text.trim(),
        email: _emailController.text.trim(),
      );

      if (success) {
        setState(() {
          _isEditing = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Profile updated successfully')),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to update profile: ${authService.lastError}',
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error updating profile: $e')));
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _updatePassword() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final authService = ref.read(authServiceProvider);

      final success = await authService.updatePassword(
        _newPasswordController.text,
      );

      if (success) {
        setState(() {
          _isChangingPassword = false;
        });
        _clearPasswordFields();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Password updated successfully')),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to update password: ${authService.lastError}',
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error updating password: $e')));
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _clearPasswordFields() {
    _currentPasswordController.clear();
    _newPasswordController.clear();
    _confirmPasswordController.clear();
  }

  Future<void> _signOut() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final authService = ref.read(authServiceProvider);
      await authService.signOut();
    }
  }
}
