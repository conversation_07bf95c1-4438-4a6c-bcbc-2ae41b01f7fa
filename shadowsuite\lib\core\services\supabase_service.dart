import 'dart:async';
import 'dart:convert';
import 'package:supabase_flutter/supabase_flutter.dart' hide User;
import 'package:connectivity_plus/connectivity_plus.dart';
import '../constants/app_constants.dart';
import '../models/user.dart';
import '../models/memo.dart';
import '../models/athkar_routine.dart';
import '../models/money_flow.dart' as models;
import 'database_service.dart';

enum SyncStatus { idle, syncing, success, error }

enum SyncConflictResolution { useLocal, useRemote, merge, skip }

class ConflictData {
  final String id;
  final String tableName;
  final Map<String, dynamic> localData;
  final Map<String, dynamic> remoteData;
  final DateTime localUpdatedAt;
  final DateTime remoteUpdatedAt;

  ConflictData({
    required this.id,
    required this.tableName,
    required this.localData,
    required this.remoteData,
    required this.localUpdatedAt,
    required this.remoteUpdatedAt,
  });
}

class SupabaseService {
  static final SupabaseService _instance = SupabaseService._internal();
  factory SupabaseService() => _instance;
  SupabaseService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  final Connectivity _connectivity = Connectivity();

  // Database services
  final UserDatabaseService _userDb = UserDatabaseService();
  final MemoDatabaseService _memoDb = MemoDatabaseService();
  final AthkarDatabaseService _athkarDb = AthkarDatabaseService();
  final AccountDatabaseService _accountDb = AccountDatabaseService();
  final CategoryDatabaseService _categoryDb = CategoryDatabaseService();
  final TransactionDatabaseService _transactionDb =
      TransactionDatabaseService();

  // Stream controllers for real-time updates
  final StreamController<List<User>> _usersController =
      StreamController<List<User>>.broadcast();
  final StreamController<List<Memo>> _memosController =
      StreamController<List<Memo>>.broadcast();
  final StreamController<List<AthkarRoutine>> _athkarController =
      StreamController<List<AthkarRoutine>>.broadcast();
  final StreamController<List<models.Account>> _accountsController =
      StreamController<List<models.Account>>.broadcast();
  final StreamController<List<models.Category>> _categoriesController =
      StreamController<List<models.Category>>.broadcast();
  final StreamController<List<models.Transaction>> _transactionsController =
      StreamController<List<models.Transaction>>.broadcast();

  // Sync status
  final StreamController<SyncStatus> _syncStatusController =
      StreamController<SyncStatus>.broadcast();
  final StreamController<String?> _syncErrorController =
      StreamController<String?>.broadcast();
  final StreamController<List<ConflictData>> _conflictsController =
      StreamController<List<ConflictData>>.broadcast();

  SyncStatus _syncStatus = SyncStatus.idle;
  String? _lastError;
  DateTime? _lastSyncTime;
  Timer? _autoSyncTimer;
  List<RealtimeChannel> _realtimeSubscriptions = [];

  // Getters
  SyncStatus get syncStatus => _syncStatus;
  String? get lastError => _lastError;
  DateTime? get lastSyncTime => _lastSyncTime;
  User? get currentUser => _supabase.auth.currentUser != null
      ? User(
          id: _supabase.auth.currentUser!.id,
          email: _supabase.auth.currentUser!.email,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        )
      : null;

  // Streams
  Stream<List<User>> get usersStream => _usersController.stream;
  Stream<List<Memo>> get memosStream => _memosController.stream;
  Stream<List<AthkarRoutine>> get athkarStream => _athkarController.stream;
  Stream<List<models.Account>> get accountsStream => _accountsController.stream;
  Stream<List<models.Category>> get categoriesStream =>
      _categoriesController.stream;
  Stream<List<models.Transaction>> get transactionsStream =>
      _transactionsController.stream;
  Stream<SyncStatus> get syncStatusStream => _syncStatusController.stream;
  Stream<String?> get syncErrorStream => _syncErrorController.stream;
  Stream<List<ConflictData>> get conflictsStream => _conflictsController.stream;

  Future<void> initialize() async {
    await _checkConnectivity();
    _setupAuthListener();
    _startAutoSync();
  }

  void _setupAuthListener() {
    _supabase.auth.onAuthStateChange.listen((data) {
      final event = data.event;
      final session = data.session;

      if (event == AuthChangeEvent.signedIn && session != null) {
        _setupRealtimeListeners();
        syncAll();
      } else if (event == AuthChangeEvent.signedOut) {
        _teardownRealtimeListeners();
        _clearLocalData();
      }
    });
  }

  void _setupRealtimeListeners() {
    final userId = _supabase.auth.currentUser?.id;
    if (userId == null) return;

    // Listen to memos changes
    final memosSubscription = _supabase
        .channel('memos_$userId')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'memos',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'user_id',
            value: userId,
          ),
          callback: (payload) => _handleRealtimeChange('memos', payload),
        )
        .subscribe();

    // Listen to athkar routines changes
    final athkarSubscription = _supabase
        .channel('athkar_routines_$userId')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'athkar_routines',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'user_id',
            value: userId,
          ),
          callback: (payload) =>
              _handleRealtimeChange('athkar_routines', payload),
        )
        .subscribe();

    // Listen to accounts changes
    final accountsSubscription = _supabase
        .channel('accounts_$userId')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'accounts',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'user_id',
            value: userId,
          ),
          callback: (payload) => _handleRealtimeChange('accounts', payload),
        )
        .subscribe();

    // Listen to categories changes
    final categoriesSubscription = _supabase
        .channel('categories_$userId')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'categories',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'user_id',
            value: userId,
          ),
          callback: (payload) => _handleRealtimeChange('categories', payload),
        )
        .subscribe();

    // Listen to transactions changes
    final transactionsSubscription = _supabase
        .channel('transactions_$userId')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'transactions',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'user_id',
            value: userId,
          ),
          callback: (payload) => _handleRealtimeChange('transactions', payload),
        )
        .subscribe();

    _realtimeSubscriptions = [
      memosSubscription,
      athkarSubscription,
      accountsSubscription,
      categoriesSubscription,
      transactionsSubscription,
    ];
  }

  void _teardownRealtimeListeners() {
    for (final channel in _realtimeSubscriptions) {
      channel.unsubscribe();
    }
    _realtimeSubscriptions.clear();
  }

  Future<void> _handleRealtimeChange(
    String tableName,
    PostgresChangePayload payload,
  ) async {
    try {
      switch (payload.eventType) {
        case PostgresChangeEvent.insert:
          await _handleRealtimeInsert(tableName, payload.newRecord);
          break;
        case PostgresChangeEvent.update:
          await _handleRealtimeUpdate(
            tableName,
            payload.newRecord,
            payload.oldRecord,
          );
          break;
        case PostgresChangeEvent.delete:
          await _handleRealtimeDelete(tableName, payload.oldRecord);
          break;
        default:
          break;
      }

      // Refresh local streams
      await _refreshLocalStreams();
    } catch (e) {
      print('Error handling realtime change for $tableName: $e');
    }
  }

  Future<bool> _checkConnectivity() async {
    final connectivityResult = await _connectivity.checkConnectivity();
    return !connectivityResult.contains(ConnectivityResult.none);
  }

  void _startAutoSync() {
    _autoSyncTimer = Timer.periodic(
      const Duration(minutes: 15), // Sync every 15 minutes
      (_) => syncAll(),
    );
  }

  Future<void> _clearLocalData() async {
    // Clear all local data when user signs out
    // This would be implemented based on requirements
  }

  Future<void> syncAll() async {
    if (_syncStatus == SyncStatus.syncing) return;

    final isConnected = await _checkConnectivity();
    if (!isConnected) {
      _updateSyncStatus(SyncStatus.error, 'No internet connection');
      return;
    }

    _updateSyncStatus(SyncStatus.syncing);

    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        _updateSyncStatus(SyncStatus.error, 'User not authenticated');
        return;
      }

      // Sync all data types
      await _syncMemos(user.id);
      await _syncAthkarRoutines(user.id);
      await _syncAccounts(user.id);
      await _syncCategories(user.id);
      await _syncTransactions(user.id);

      _lastSyncTime = DateTime.now();
      _updateSyncStatus(SyncStatus.success);
    } catch (e) {
      _updateSyncStatus(SyncStatus.error, e.toString());
    }
  }

  void _updateSyncStatus(SyncStatus status, [String? error]) {
    _syncStatus = status;
    _lastError = error;
    _syncStatusController.add(status);
    if (error != null) {
      _syncErrorController.add(error);
    }
  }

  Future<void> _refreshLocalStreams() async {
    final userId = _supabase.auth.currentUser?.id;
    if (userId == null) return;

    // Refresh all streams with latest local data
    final memos = await _memoDb.getAll(userId: userId);
    _memosController.add(memos);

    final routines = await _athkarDb.getAll(userId: userId);
    _athkarController.add(routines);

    final accounts = await _accountDb.getAll(userId: userId);
    _accountsController.add(accounts);

    final categories = await _categoryDb.getAll(userId: userId);
    _categoriesController.add(categories);

    final transactions = await _transactionDb.getAll(userId: userId);
    _transactionsController.add(transactions);
  }

  Future<void> _handleRealtimeInsert(
    String tableName,
    Map<String, dynamic> record,
  ) async {
    switch (tableName) {
      case 'memos':
        final memo = Memo.fromJson(record);
        await _memoDb.insert(memo.copyWith(isSynced: true));
        break;
      case 'athkar_routines':
        final routine = AthkarRoutine.fromJson(record);
        await _athkarDb.insert(routine.copyWith(isSynced: true));
        break;
      case 'accounts':
        final account = models.Account.fromJson(record);
        await _accountDb.insert(account.copyWith(isSynced: true));
        break;
      case 'categories':
        final category = models.Category.fromJson(record);
        await _categoryDb.insert(category.copyWith(isSynced: true));
        break;
      case 'transactions':
        final transaction = models.Transaction.fromJson(record);
        await _transactionDb.insert(transaction.copyWith(isSynced: true));
        break;
    }
  }

  Future<void> _handleRealtimeUpdate(
    String tableName,
    Map<String, dynamic> newRecord,
    Map<String, dynamic>? oldRecord,
  ) async {
    switch (tableName) {
      case 'memos':
        final memo = Memo.fromJson(newRecord);
        final existingMemo = await _memoDb.getById(memo.id);
        if (existingMemo != null) {
          if (existingMemo.updatedAt.isAfter(memo.updatedAt) &&
              !existingMemo.isSynced) {
            await _handleConflict(
              'memos',
              memo.id,
              existingMemo.toMap(),
              newRecord,
            );
          } else {
            await _memoDb.update(memo.copyWith(isSynced: true));
          }
        }
        break;
      case 'athkar_routines':
        final routine = AthkarRoutine.fromJson(newRecord);
        final existingRoutine = await _athkarDb.getById(routine.id);
        if (existingRoutine != null) {
          if (existingRoutine.updatedAt.isAfter(routine.updatedAt) &&
              !existingRoutine.isSynced) {
            await _handleConflict(
              'athkar_routines',
              routine.id,
              existingRoutine.toMap(),
              newRecord,
            );
          } else {
            await _athkarDb.update(routine.copyWith(isSynced: true));
          }
        }
        break;
      case 'accounts':
        final account = models.Account.fromJson(newRecord);
        final existingAccount = await _accountDb.getById(account.id);
        if (existingAccount != null) {
          if (existingAccount.updatedAt.isAfter(account.updatedAt) &&
              !existingAccount.isSynced) {
            await _handleConflict(
              'accounts',
              account.id,
              existingAccount.toMap(),
              newRecord,
            );
          } else {
            await _accountDb.update(account.copyWith(isSynced: true));
          }
        }
        break;
      case 'categories':
        final category = models.Category.fromJson(newRecord);
        final existingCategory = await _categoryDb.getById(category.id);
        if (existingCategory != null) {
          if (existingCategory.updatedAt.isAfter(category.updatedAt) &&
              !existingCategory.isSynced) {
            await _handleConflict(
              'categories',
              category.id,
              existingCategory.toMap(),
              newRecord,
            );
          } else {
            await _categoryDb.update(category.copyWith(isSynced: true));
          }
        }
        break;
      case 'transactions':
        final transaction = models.Transaction.fromJson(newRecord);
        final existingTransaction = await _transactionDb.getById(
          transaction.id,
        );
        if (existingTransaction != null) {
          if (existingTransaction.updatedAt.isAfter(transaction.updatedAt) &&
              !existingTransaction.isSynced) {
            await _handleConflict(
              'transactions',
              transaction.id,
              existingTransaction.toMap(),
              newRecord,
            );
          } else {
            await _transactionDb.update(transaction.copyWith(isSynced: true));
          }
        }
        break;
    }
  }

  Future<void> _handleRealtimeDelete(
    String tableName,
    Map<String, dynamic>? record,
  ) async {
    if (record == null) return;

    final id = record['id'] as String?;
    if (id == null) return;

    switch (tableName) {
      case 'memos':
        await _memoDb.delete(id);
        break;
      case 'athkar_routines':
        await _athkarDb.delete(id);
        break;
      case 'accounts':
        await _accountDb.delete(id);
        break;
      case 'categories':
        await _categoryDb.delete(id);
        break;
      case 'transactions':
        await _transactionDb.delete(id);
        break;
    }
  }

  Future<void> _handleConflict(
    String tableName,
    String id,
    Map<String, dynamic> localData,
    Map<String, dynamic> remoteData,
  ) async {
    final conflict = ConflictData(
      id: id,
      tableName: tableName,
      localData: localData,
      remoteData: remoteData,
      localUpdatedAt: DateTime.parse(localData['updated_at'] as String),
      remoteUpdatedAt: DateTime.parse(remoteData['updated_at'] as String),
    );

    // Add to conflicts stream for UI to handle
    final currentConflicts = <ConflictData>[];
    try {
      // Get current conflicts if any
      currentConflicts.addAll(
        _conflictsController.stream.last as Iterable<ConflictData>,
      );
    } catch (e) {
      // No current conflicts
    }

    currentConflicts.add(conflict);
    _conflictsController.add(currentConflicts);
  }

  Future<void> resolveConflict(
    String conflictId,
    SyncConflictResolution resolution,
  ) async {
    // Implementation for conflict resolution
    // This would be called from the UI when user chooses how to resolve conflicts
  }

  // Individual sync methods
  Future<void> _syncMemos(String userId) async {
    try {
      // Upload unsynced local memos
      final localMemos = await _memoDb.getAll(userId: userId);
      final unsyncedMemos = localMemos.where((memo) => !memo.isSynced).toList();

      for (final memo in unsyncedMemos) {
        await _supabase.from('memos').upsert(memo.toJson());
        await _memoDb.update(memo.copyWith(isSynced: true));
      }

      // Download remote memos
      final remoteMemos = await _supabase
          .from('memos')
          .select()
          .eq('user_id', userId);

      for (final memoData in remoteMemos) {
        final memo = Memo.fromJson(memoData);
        final existingMemo = await _memoDb.getById(memo.id);

        if (existingMemo == null) {
          await _memoDb.insert(memo.copyWith(isSynced: true));
        } else if (memo.updatedAt.isAfter(existingMemo.updatedAt)) {
          await _memoDb.update(memo.copyWith(isSynced: true));
        }
      }
    } catch (e) {
      print('Error syncing memos: $e');
      rethrow;
    }
  }

  Future<void> _syncAthkarRoutines(String userId) async {
    try {
      // Upload unsynced local routines
      final localRoutines = await _athkarDb.getAll(userId: userId);
      final unsyncedRoutines = localRoutines
          .where((routine) => !routine.isSynced)
          .toList();

      for (final routine in unsyncedRoutines) {
        await _supabase.from('athkar_routines').upsert(routine.toJson());
        await _athkarDb.update(routine.copyWith(isSynced: true));
      }

      // Download remote routines
      final remoteRoutines = await _supabase
          .from('athkar_routines')
          .select()
          .eq('user_id', userId);

      for (final routineData in remoteRoutines) {
        final routine = AthkarRoutine.fromJson(routineData);
        final existingRoutine = await _athkarDb.getById(routine.id);

        if (existingRoutine == null) {
          await _athkarDb.insert(routine.copyWith(isSynced: true));
        } else if (routine.updatedAt.isAfter(existingRoutine.updatedAt)) {
          await _athkarDb.update(routine.copyWith(isSynced: true));
        }
      }
    } catch (e) {
      print('Error syncing athkar routines: $e');
      rethrow;
    }
  }

  Future<void> _syncAccounts(String userId) async {
    try {
      final localAccounts = await _accountDb.getAll(userId: userId);
      final unsyncedAccounts = localAccounts
          .where((account) => !account.isSynced)
          .toList();

      for (final account in unsyncedAccounts) {
        await _supabase.from('accounts').upsert(account.toJson());
        await _accountDb.update(account.copyWith(isSynced: true));
      }

      final remoteAccounts = await _supabase
          .from('accounts')
          .select()
          .eq('user_id', userId);

      for (final accountData in remoteAccounts) {
        final account = models.Account.fromJson(accountData);
        final existingAccount = await _accountDb.getById(account.id);

        if (existingAccount == null) {
          await _accountDb.insert(account.copyWith(isSynced: true));
        } else if (account.updatedAt.isAfter(existingAccount.updatedAt)) {
          await _accountDb.update(account.copyWith(isSynced: true));
        }
      }
    } catch (e) {
      print('Error syncing accounts: $e');
      rethrow;
    }
  }

  Future<void> _syncCategories(String userId) async {
    try {
      final localCategories = await _categoryDb.getAll(userId: userId);
      final unsyncedCategories = localCategories
          .where((category) => !category.isSynced)
          .toList();

      for (final category in unsyncedCategories) {
        await _supabase.from('categories').upsert(category.toJson());
        await _categoryDb.update(category.copyWith(isSynced: true));
      }

      final remoteCategories = await _supabase
          .from('categories')
          .select()
          .eq('user_id', userId);

      for (final categoryData in remoteCategories) {
        final category = models.Category.fromJson(categoryData);
        final existingCategory = await _categoryDb.getById(category.id);

        if (existingCategory == null) {
          await _categoryDb.insert(category.copyWith(isSynced: true));
        } else if (category.updatedAt.isAfter(existingCategory.updatedAt)) {
          await _categoryDb.update(category.copyWith(isSynced: true));
        }
      }
    } catch (e) {
      print('Error syncing categories: $e');
      rethrow;
    }
  }

  Future<void> _syncTransactions(String userId) async {
    try {
      final localTransactions = await _transactionDb.getAll(userId: userId);
      final unsyncedTransactions = localTransactions
          .where((transaction) => !transaction.isSynced)
          .toList();

      for (final transaction in unsyncedTransactions) {
        await _supabase.from('transactions').upsert(transaction.toJson());
        await _transactionDb.update(transaction.copyWith(isSynced: true));
      }

      final remoteTransactions = await _supabase
          .from('transactions')
          .select()
          .eq('user_id', userId);

      for (final transactionData in remoteTransactions) {
        final transaction = models.Transaction.fromJson(transactionData);
        final existingTransaction = await _transactionDb.getById(
          transaction.id,
        );

        if (existingTransaction == null) {
          await _transactionDb.insert(transaction.copyWith(isSynced: true));
        } else if (transaction.updatedAt.isAfter(
          existingTransaction.updatedAt,
        )) {
          await _transactionDb.update(transaction.copyWith(isSynced: true));
        }
      }
    } catch (e) {
      print('Error syncing transactions: $e');
      rethrow;
    }
  }

  // Authentication methods
  Future<bool> signIn(String email, String password) async {
    try {
      final response = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user != null) {
        await syncAll();
        return true;
      }
      return false;
    } catch (e) {
      _updateSyncStatus(SyncStatus.error, e.toString());
      return false;
    }
  }

  Future<bool> signUp(String email, String password, String name) async {
    try {
      final response = await _supabase.auth.signUp(
        email: email,
        password: password,
      );

      if (response.user != null) {
        // Create user record
        final user = User(
          id: response.user!.id,
          email: email,
          name: name,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await _userDb.insert(user);
        await syncAll();
        return true;
      }
      return false;
    } catch (e) {
      _updateSyncStatus(SyncStatus.error, e.toString());
      return false;
    }
  }

  Future<void> signOut() async {
    try {
      await _supabase.auth.signOut();
      _teardownRealtimeListeners();
      _lastSyncTime = null;
      _updateSyncStatus(SyncStatus.idle);
    } catch (e) {
      print('Error signing out: $e');
    }
  }

  void dispose() {
    _autoSyncTimer?.cancel();
    _teardownRealtimeListeners();
    _usersController.close();
    _memosController.close();
    _athkarController.close();
    _accountsController.close();
    _categoriesController.close();
    _transactionsController.close();
    _syncStatusController.close();
    _syncErrorController.close();
    _conflictsController.close();
  }
}
