// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'memo.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Memo _$MemoFromJson(Map<String, dynamic> json) => Memo(
  id: json['id'] as String,
  userId: json['userId'] as String?,
  title: json['title'] as String,
  description: json['description'] as String?,
  audioPath: json['audioPath'] as String?,
  transcription: json['transcription'] as String?,
  duration: (json['duration'] as num?)?.toInt() ?? 0,
  fileSize: (json['fileSize'] as num?)?.toInt() ?? 0,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  isSynced: json['isSynced'] as bool? ?? false,
);

Map<String, dynamic> _$MemoToJson(Memo instance) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'title': instance.title,
  'description': instance.description,
  'audioPath': instance.audioPath,
  'transcription': instance.transcription,
  'duration': instance.duration,
  'fileSize': instance.fileSize,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'isSynced': instance.isSynced,
};
