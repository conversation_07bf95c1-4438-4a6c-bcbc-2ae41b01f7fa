[{"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/animator/fragment_fade_exit.xml", "source": "eu.simonbinder.sqlite3_flutter_libs-fragment-1.7.1-17:/animator/fragment_fade_exit.xml"}, {"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/animator/fragment_open_enter.xml", "source": "eu.simonbinder.sqlite3_flutter_libs-fragment-1.7.1-17:/animator/fragment_open_enter.xml"}, {"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/animator/fragment_close_enter.xml", "source": "eu.simonbinder.sqlite3_flutter_libs-fragment-1.7.1-17:/animator/fragment_close_enter.xml"}, {"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/animator/fragment_close_exit.xml", "source": "eu.simonbinder.sqlite3_flutter_libs-fragment-1.7.1-17:/animator/fragment_close_exit.xml"}, {"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/animator/fragment_fade_enter.xml", "source": "eu.simonbinder.sqlite3_flutter_libs-fragment-1.7.1-17:/animator/fragment_fade_enter.xml"}, {"merged": "eu.simonbinder.sqlite3_flutter_libs-release-25:/animator/fragment_open_exit.xml", "source": "eu.simonbinder.sqlite3_flutter_libs-fragment-1.7.1-17:/animator/fragment_open_exit.xml"}]