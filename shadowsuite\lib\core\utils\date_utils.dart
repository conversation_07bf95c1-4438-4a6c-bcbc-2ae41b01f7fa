import 'package:intl/intl.dart';
import '../constants/app_constants.dart';

class AppDateUtils {
  static final DateFormat _dateFormat = DateFormat(AppConstants.dateFormat);
  static final DateFormat _timeFormat = DateFormat(AppConstants.timeFormat);
  static final DateFormat _dateTimeFormat = DateFormat(AppConstants.dateTimeFormat);
  static final DateFormat _displayDateFormat = DateFormat(AppConstants.displayDateFormat);
  static final DateFormat _displayTimeFormat = DateFormat(AppConstants.displayTimeFormat);

  // Format dates for storage
  static String formatDateForStorage(DateTime date) {
    return _dateFormat.format(date);
  }

  static String formatTimeForStorage(DateTime time) {
    return _timeFormat.format(time);
  }

  static String formatDateTimeForStorage(DateTime dateTime) {
    return _dateTimeFormat.format(dateTime);
  }

  // Format dates for display
  static String formatDateForDisplay(DateTime date) {
    return _displayDateFormat.format(date);
  }

  static String formatTimeForDisplay(DateTime time) {
    return _displayTimeFormat.format(time);
  }

  static String formatDateTimeForDisplay(DateTime dateTime) {
    return '${formatDateForDisplay(dateTime)} ${formatTimeForDisplay(dateTime)}';
  }

  // Parse dates from storage
  static DateTime? parseDateFromStorage(String dateString) {
    try {
      return _dateFormat.parse(dateString);
    } catch (e) {
      return null;
    }
  }

  static DateTime? parseTimeFromStorage(String timeString) {
    try {
      return _timeFormat.parse(timeString);
    } catch (e) {
      return null;
    }
  }

  static DateTime? parseDateTimeFromStorage(String dateTimeString) {
    try {
      return _dateTimeFormat.parse(dateTimeString);
    } catch (e) {
      return null;
    }
  }

  // Relative time formatting
  static String getRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 365) {
      final years = (difference.inDays / 365).floor();
      return years == 1 ? '1 year ago' : '$years years ago';
    } else if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return months == 1 ? '1 month ago' : '$months months ago';
    } else if (difference.inDays > 0) {
      return difference.inDays == 1 ? '1 day ago' : '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return difference.inHours == 1 ? '1 hour ago' : '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      return difference.inMinutes == 1 ? '1 minute ago' : '${difference.inMinutes} minutes ago';
    } else {
      return 'Just now';
    }
  }

  // Date range helpers
  static DateTime startOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  static DateTime endOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day, 23, 59, 59, 999);
  }

  static DateTime startOfWeek(DateTime date) {
    final daysFromMonday = date.weekday - 1;
    return startOfDay(date.subtract(Duration(days: daysFromMonday)));
  }

  static DateTime endOfWeek(DateTime date) {
    final daysToSunday = 7 - date.weekday;
    return endOfDay(date.add(Duration(days: daysToSunday)));
  }

  static DateTime startOfMonth(DateTime date) {
    return DateTime(date.year, date.month, 1);
  }

  static DateTime endOfMonth(DateTime date) {
    return DateTime(date.year, date.month + 1, 0, 23, 59, 59, 999);
  }

  static DateTime startOfYear(DateTime date) {
    return DateTime(date.year, 1, 1);
  }

  static DateTime endOfYear(DateTime date) {
    return DateTime(date.year, 12, 31, 23, 59, 59, 999);
  }

  // Date comparison helpers
  static bool isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  static bool isSameWeek(DateTime date1, DateTime date2) {
    final startOfWeek1 = startOfWeek(date1);
    final startOfWeek2 = startOfWeek(date2);
    return isSameDay(startOfWeek1, startOfWeek2);
  }

  static bool isSameMonth(DateTime date1, DateTime date2) {
    return date1.year == date2.year && date1.month == date2.month;
  }

  static bool isSameYear(DateTime date1, DateTime date2) {
    return date1.year == date2.year;
  }

  static bool isToday(DateTime date) {
    return isSameDay(date, DateTime.now());
  }

  static bool isYesterday(DateTime date) {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return isSameDay(date, yesterday);
  }

  static bool isTomorrow(DateTime date) {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    return isSameDay(date, tomorrow);
  }

  static bool isThisWeek(DateTime date) {
    return isSameWeek(date, DateTime.now());
  }

  static bool isThisMonth(DateTime date) {
    return isSameMonth(date, DateTime.now());
  }

  static bool isThisYear(DateTime date) {
    return isSameYear(date, DateTime.now());
  }

  // Duration formatting
  static String formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:'
             '${minutes.toString().padLeft(2, '0')}:'
             '${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:'
             '${seconds.toString().padLeft(2, '0')}';
    }
  }

  static String formatDurationInWords(Duration duration) {
    final days = duration.inDays;
    final hours = duration.inHours.remainder(24);
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    final parts = <String>[];

    if (days > 0) {
      parts.add(days == 1 ? '1 day' : '$days days');
    }
    if (hours > 0) {
      parts.add(hours == 1 ? '1 hour' : '$hours hours');
    }
    if (minutes > 0) {
      parts.add(minutes == 1 ? '1 minute' : '$minutes minutes');
    }
    if (seconds > 0 && parts.isEmpty) {
      parts.add(seconds == 1 ? '1 second' : '$seconds seconds');
    }

    if (parts.isEmpty) {
      return '0 seconds';
    } else if (parts.length == 1) {
      return parts.first;
    } else if (parts.length == 2) {
      return '${parts[0]} and ${parts[1]}';
    } else {
      return '${parts.take(parts.length - 1).join(', ')}, and ${parts.last}';
    }
  }

  // Age calculation
  static int calculateAge(DateTime birthDate) {
    final now = DateTime.now();
    int age = now.year - birthDate.year;
    
    if (now.month < birthDate.month ||
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    
    return age;
  }

  // Business days calculation
  static int getBusinessDaysBetween(DateTime startDate, DateTime endDate) {
    if (startDate.isAfter(endDate)) {
      return 0;
    }

    int businessDays = 0;
    DateTime current = startDate;

    while (current.isBefore(endDate) || current.isAtSameMomentAs(endDate)) {
      if (current.weekday >= 1 && current.weekday <= 5) {
        businessDays++;
      }
      current = current.add(const Duration(days: 1));
    }

    return businessDays;
  }

  // Week number calculation
  static int getWeekNumber(DateTime date) {
    final startOfYear = DateTime(date.year, 1, 1);
    final firstMonday = startOfYear.add(Duration(days: (8 - startOfYear.weekday) % 7));
    
    if (date.isBefore(firstMonday)) {
      return getWeekNumber(DateTime(date.year - 1, 12, 31));
    }
    
    return ((date.difference(firstMonday).inDays) / 7).floor() + 1;
  }

  // Quarter calculation
  static int getQuarter(DateTime date) {
    return ((date.month - 1) / 3).floor() + 1;
  }

  static DateTime getQuarterStart(DateTime date) {
    final quarter = getQuarter(date);
    final startMonth = (quarter - 1) * 3 + 1;
    return DateTime(date.year, startMonth, 1);
  }

  static DateTime getQuarterEnd(DateTime date) {
    final quarter = getQuarter(date);
    final endMonth = quarter * 3;
    return DateTime(date.year, endMonth + 1, 0, 23, 59, 59, 999);
  }

  // Time zone helpers
  static DateTime toUtc(DateTime localDateTime) {
    return localDateTime.toUtc();
  }

  static DateTime toLocal(DateTime utcDateTime) {
    return utcDateTime.toLocal();
  }

  // Validation helpers
  static bool isValidDate(String dateString) {
    try {
      _dateFormat.parseStrict(dateString);
      return true;
    } catch (e) {
      return false;
    }
  }

  static bool isValidTime(String timeString) {
    try {
      _timeFormat.parseStrict(timeString);
      return true;
    } catch (e) {
      return false;
    }
  }

  static bool isValidDateTime(String dateTimeString) {
    try {
      _dateTimeFormat.parseStrict(dateTimeString);
      return true;
    } catch (e) {
      return false;
    }
  }
}
