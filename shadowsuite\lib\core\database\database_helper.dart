import 'dart:io';
import 'package:sqflite/sqflite.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import '../constants/app_constants.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  factory DatabaseHelper() => _instance;

  DatabaseHelper._internal();

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    // Initialize FFI for desktop platforms
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    }

    final documentsDirectory = await getApplicationDocumentsDirectory();
    final path = join(documentsDirectory.path, AppConstants.databaseName);

    return await openDatabase(
      path,
      version: AppConstants.databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Users table
    await db.execute('''
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE,
        name TEXT,
        avatar_url TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        last_sync_at TEXT,
        is_synced INTEGER DEFAULT 0
      )
    ''');

    // Memo Suite tables
    await db.execute('''
      CREATE TABLE memos (
        id TEXT PRIMARY KEY,
        user_id TEXT,
        title TEXT NOT NULL,
        description TEXT,
        audio_path TEXT,
        transcription TEXT,
        duration INTEGER DEFAULT 0,
        file_size INTEGER DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        is_synced INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    // Athkar Pro tables
    await db.execute('''
      CREATE TABLE athkar_routines (
        id TEXT PRIMARY KEY,
        user_id TEXT,
        title TEXT NOT NULL,
        description TEXT,
        target_count INTEGER NOT NULL DEFAULT 1,
        current_count INTEGER DEFAULT 0,
        is_completed INTEGER DEFAULT 0,
        reminder_enabled INTEGER DEFAULT 0,
        reminder_times TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        is_synced INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE athkar_items (
        id TEXT PRIMARY KEY,
        routine_id TEXT NOT NULL,
        arabic_text TEXT NOT NULL,
        transliteration TEXT,
        translation TEXT,
        count INTEGER NOT NULL DEFAULT 1,
        order_index INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        is_synced INTEGER DEFAULT 0,
        FOREIGN KEY (routine_id) REFERENCES athkar_routines (id) ON DELETE CASCADE
      )
    ''');

    await db.execute('''
      CREATE TABLE athkar_progress (
        id TEXT PRIMARY KEY,
        routine_id TEXT NOT NULL,
        date TEXT NOT NULL,
        completed_count INTEGER DEFAULT 0,
        target_count INTEGER NOT NULL,
        is_completed INTEGER DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        is_synced INTEGER DEFAULT 0,
        FOREIGN KEY (routine_id) REFERENCES athkar_routines (id) ON DELETE CASCADE,
        UNIQUE(routine_id, date)
      )
    ''');

    // Money Flow tables
    await db.execute('''
      CREATE TABLE accounts (
        id TEXT PRIMARY KEY,
        user_id TEXT,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        balance REAL DEFAULT 0.0,
        currency TEXT DEFAULT 'USD',
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        is_synced INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE categories (
        id TEXT PRIMARY KEY,
        user_id TEXT,
        name TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
        color TEXT,
        icon TEXT,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        is_synced INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE transactions (
        id TEXT PRIMARY KEY,
        user_id TEXT,
        account_id TEXT NOT NULL,
        category_id TEXT NOT NULL,
        amount REAL NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('income', 'expense', 'transfer')),
        description TEXT,
        date TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        is_synced INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (account_id) REFERENCES accounts (id),
        FOREIGN KEY (category_id) REFERENCES categories (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE budgets (
        id TEXT PRIMARY KEY,
        user_id TEXT,
        category_id TEXT NOT NULL,
        amount REAL NOT NULL,
        period TEXT NOT NULL CHECK (period IN ('daily', 'weekly', 'monthly', 'yearly')),
        start_date TEXT NOT NULL,
        end_date TEXT NOT NULL,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        is_synced INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (category_id) REFERENCES categories (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE recurring_transactions (
        id TEXT PRIMARY KEY,
        user_id TEXT,
        account_id TEXT NOT NULL,
        category_id TEXT NOT NULL,
        amount REAL NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
        description TEXT,
        frequency TEXT NOT NULL CHECK (frequency IN ('daily', 'weekly', 'monthly', 'yearly')),
        start_date TEXT NOT NULL,
        end_date TEXT,
        next_date TEXT NOT NULL,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        is_synced INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (account_id) REFERENCES accounts (id),
        FOREIGN KEY (category_id) REFERENCES categories (id)
      )
    ''');

    // Sync log table
    await db.execute('''
      CREATE TABLE sync_log (
        id TEXT PRIMARY KEY,
        table_name TEXT NOT NULL,
        record_id TEXT NOT NULL,
        action TEXT NOT NULL CHECK (action IN ('create', 'update', 'delete')),
        sync_status TEXT NOT NULL CHECK (sync_status IN ('pending', 'synced', 'failed')),
        error_message TEXT,
        created_at TEXT NOT NULL,
        synced_at TEXT
      )
    ''');

    // Create indexes for better performance
    await _createIndexes(db);

    // Insert default data
    await _insertDefaultData(db);
  }

  Future<void> _createIndexes(Database db) async {
    // User indexes
    await db.execute('CREATE INDEX idx_users_email ON users(email)');
    
    // Memo indexes
    await db.execute('CREATE INDEX idx_memos_user_id ON memos(user_id)');
    await db.execute('CREATE INDEX idx_memos_created_at ON memos(created_at)');
    
    // Athkar indexes
    await db.execute('CREATE INDEX idx_athkar_routines_user_id ON athkar_routines(user_id)');
    await db.execute('CREATE INDEX idx_athkar_items_routine_id ON athkar_items(routine_id)');
    await db.execute('CREATE INDEX idx_athkar_progress_routine_id ON athkar_progress(routine_id)');
    await db.execute('CREATE INDEX idx_athkar_progress_date ON athkar_progress(date)');
    
    // Money Flow indexes
    await db.execute('CREATE INDEX idx_accounts_user_id ON accounts(user_id)');
    await db.execute('CREATE INDEX idx_categories_user_id ON categories(user_id)');
    await db.execute('CREATE INDEX idx_transactions_user_id ON transactions(user_id)');
    await db.execute('CREATE INDEX idx_transactions_account_id ON transactions(account_id)');
    await db.execute('CREATE INDEX idx_transactions_category_id ON transactions(category_id)');
    await db.execute('CREATE INDEX idx_transactions_date ON transactions(date)');
    await db.execute('CREATE INDEX idx_budgets_user_id ON budgets(user_id)');
    await db.execute('CREATE INDEX idx_recurring_transactions_user_id ON recurring_transactions(user_id)');
    
    // Sync indexes
    await db.execute('CREATE INDEX idx_sync_log_table_record ON sync_log(table_name, record_id)');
    await db.execute('CREATE INDEX idx_sync_log_status ON sync_log(sync_status)');
  }

  Future<void> _insertDefaultData(Database db) async {
    final now = DateTime.now().toIso8601String();
    
    // Insert default income categories
    for (final category in AppConstants.defaultIncomeCategories) {
      await db.insert('categories', {
        'id': 'income_${category.toLowerCase().replaceAll(' ', '_')}_default',
        'user_id': null,
        'name': category,
        'type': 'income',
        'color': '#4CAF50',
        'icon': 'attach_money',
        'is_active': 1,
        'created_at': now,
        'updated_at': now,
        'is_synced': 0,
      });
    }
    
    // Insert default expense categories
    for (final category in AppConstants.defaultExpenseCategories) {
      await db.insert('categories', {
        'id': 'expense_${category.toLowerCase().replaceAll(' ', '_')}_default',
        'user_id': null,
        'name': category,
        'type': 'expense',
        'color': '#F44336',
        'icon': 'shopping_cart',
        'is_active': 1,
        'created_at': now,
        'updated_at': now,
        'is_synced': 0,
      });
    }
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < 2) {
      // Add new columns or tables for version 2
    }
  }

  Future<void> close() async {
    final db = await database;
    await db.close();
    _database = null;
  }

  Future<void> deleteDatabase() async {
    final documentsDirectory = await getApplicationDocumentsDirectory();
    final path = join(documentsDirectory.path, AppConstants.databaseName);
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }
}
