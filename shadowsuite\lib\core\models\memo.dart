import 'package:json_annotation/json_annotation.dart';

part 'memo.g.dart';

@JsonSerializable()
class Memo {
  final String id;
  final String? userId;
  final String title;
  final String? description;
  final String? audioPath;
  final String? transcription;
  final int duration; // in seconds
  final int fileSize; // in bytes
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isSynced;

  const Memo({
    required this.id,
    this.userId,
    required this.title,
    this.description,
    this.audioPath,
    this.transcription,
    this.duration = 0,
    this.fileSize = 0,
    required this.createdAt,
    required this.updatedAt,
    this.isSynced = false,
  });

  factory Memo.fromJson(Map<String, dynamic> json) => _$MemoFromJson(json);

  Map<String, dynamic> toJson() => _$MemoToJson(this);

  factory Memo.fromMap(Map<String, dynamic> map) {
    return Memo(
      id: map['id'] as String,
      userId: map['user_id'] as String?,
      title: map['title'] as String,
      description: map['description'] as String?,
      audioPath: map['audio_path'] as String?,
      transcription: map['transcription'] as String?,
      duration: map['duration'] as int? ?? 0,
      fileSize: map['file_size'] as int? ?? 0,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
      isSynced: (map['is_synced'] as int) == 1,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'title': title,
      'description': description,
      'audio_path': audioPath,
      'transcription': transcription,
      'duration': duration,
      'file_size': fileSize,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_synced': isSynced ? 1 : 0,
    };
  }

  Memo copyWith({
    String? id,
    String? userId,
    String? title,
    String? description,
    String? audioPath,
    String? transcription,
    int? duration,
    int? fileSize,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSynced,
  }) {
    return Memo(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      description: description ?? this.description,
      audioPath: audioPath ?? this.audioPath,
      transcription: transcription ?? this.transcription,
      duration: duration ?? this.duration,
      fileSize: fileSize ?? this.fileSize,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  String get formattedDuration {
    final minutes = duration ~/ 60;
    final seconds = duration % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  String get formattedFileSize {
    if (fileSize < 1024) {
      return '$fileSize B';
    } else if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }

  bool get hasAudio => audioPath != null && audioPath!.isNotEmpty;
  bool get hasTranscription => transcription != null && transcription!.isNotEmpty;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Memo && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Memo(id: $id, userId: $userId, title: $title, description: $description, '
           'audioPath: $audioPath, transcription: $transcription, duration: $duration, '
           'fileSize: $fileSize, createdAt: $createdAt, updatedAt: $updatedAt, '
           'isSynced: $isSynced)';
  }
}
