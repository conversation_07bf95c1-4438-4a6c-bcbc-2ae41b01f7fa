["D:\\apps\\New folder (3)\\shadowsuite\\lib/l10n\\app_localizations_ar.dart", "D:\\apps\\New folder (3)\\shadowsuite\\lib/l10n\\app_localizations_en.dart", "D:\\apps\\New folder (3)\\shadowsuite\\lib/l10n\\app_localizations.dart", "D:\\apps\\New folder (3)\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "D:\\apps\\New folder (3)\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\packages/record_web/assets/js/record.worklet.js", "D:\\apps\\New folder (3)\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\packages/record_web/assets/js/record.fixwebmduration.js", "D:\\apps\\New folder (3)\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "D:\\apps\\New folder (3)\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\shaders/ink_sparkle.frag", "D:\\apps\\New folder (3)\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\AssetManifest.json", "D:\\apps\\New folder (3)\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\AssetManifest.bin", "D:\\apps\\New folder (3)\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\FontManifest.json", "D:\\apps\\New folder (3)\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\NOTICES.Z", "D:\\apps\\New folder (3)\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\NativeAssetsManifest.json", "D:\\apps\\New folder (3)\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\arm64-v8a\\app.so", "D:\\apps\\New folder (3)\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\x86_64\\app.so", "D:\\apps\\New folder (3)\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\armeabi-v7a\\app.so"]