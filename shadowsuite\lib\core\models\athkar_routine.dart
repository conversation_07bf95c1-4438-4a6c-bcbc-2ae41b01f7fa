import 'package:json_annotation/json_annotation.dart';

part 'athkar_routine.g.dart';

@JsonSerializable()
class AthkarRoutine {
  final String id;
  final String? userId;
  final String title;
  final String? description;
  final int targetCount;
  final int currentCount;
  final bool isCompleted;
  final bool reminderEnabled;
  final List<String>? reminderTimes;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isSynced;

  const AthkarRoutine({
    required this.id,
    this.userId,
    required this.title,
    this.description,
    this.targetCount = 1,
    this.currentCount = 0,
    this.isCompleted = false,
    this.reminderEnabled = false,
    this.reminderTimes,
    required this.createdAt,
    required this.updatedAt,
    this.isSynced = false,
  });

  factory AthkarRoutine.fromJson(Map<String, dynamic> json) => _$AthkarRoutineFromJson(json);

  Map<String, dynamic> toJson() => _$AthkarRoutineToJson(this);

  factory AthkarRoutine.fromMap(Map<String, dynamic> map) {
    return AthkarRoutine(
      id: map['id'] as String,
      userId: map['user_id'] as String?,
      title: map['title'] as String,
      description: map['description'] as String?,
      targetCount: map['target_count'] as int? ?? 1,
      currentCount: map['current_count'] as int? ?? 0,
      isCompleted: (map['is_completed'] as int) == 1,
      reminderEnabled: (map['reminder_enabled'] as int) == 1,
      reminderTimes: map['reminder_times'] != null 
          ? (map['reminder_times'] as String).split(',')
          : null,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
      isSynced: (map['is_synced'] as int) == 1,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'title': title,
      'description': description,
      'target_count': targetCount,
      'current_count': currentCount,
      'is_completed': isCompleted ? 1 : 0,
      'reminder_enabled': reminderEnabled ? 1 : 0,
      'reminder_times': reminderTimes?.join(','),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_synced': isSynced ? 1 : 0,
    };
  }

  AthkarRoutine copyWith({
    String? id,
    String? userId,
    String? title,
    String? description,
    int? targetCount,
    int? currentCount,
    bool? isCompleted,
    bool? reminderEnabled,
    List<String>? reminderTimes,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSynced,
  }) {
    return AthkarRoutine(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      description: description ?? this.description,
      targetCount: targetCount ?? this.targetCount,
      currentCount: currentCount ?? this.currentCount,
      isCompleted: isCompleted ?? this.isCompleted,
      reminderEnabled: reminderEnabled ?? this.reminderEnabled,
      reminderTimes: reminderTimes ?? this.reminderTimes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  double get progressPercentage {
    if (targetCount == 0) return 0.0;
    return (currentCount / targetCount).clamp(0.0, 1.0);
  }

  int get remainingCount {
    return (targetCount - currentCount).clamp(0, targetCount);
  }

  bool get canIncrement => currentCount < targetCount;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AthkarRoutine && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'AthkarRoutine(id: $id, userId: $userId, title: $title, '
           'description: $description, targetCount: $targetCount, '
           'currentCount: $currentCount, isCompleted: $isCompleted, '
           'reminderEnabled: $reminderEnabled, reminderTimes: $reminderTimes, '
           'createdAt: $createdAt, updatedAt: $updatedAt, isSynced: $isSynced)';
  }
}

@JsonSerializable()
class AthkarItem {
  final String id;
  final String routineId;
  final String arabicText;
  final String? transliteration;
  final String? translation;
  final int count;
  final int orderIndex;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isSynced;

  const AthkarItem({
    required this.id,
    required this.routineId,
    required this.arabicText,
    this.transliteration,
    this.translation,
    this.count = 1,
    this.orderIndex = 0,
    required this.createdAt,
    required this.updatedAt,
    this.isSynced = false,
  });

  factory AthkarItem.fromJson(Map<String, dynamic> json) => _$AthkarItemFromJson(json);

  Map<String, dynamic> toJson() => _$AthkarItemToJson(this);

  factory AthkarItem.fromMap(Map<String, dynamic> map) {
    return AthkarItem(
      id: map['id'] as String,
      routineId: map['routine_id'] as String,
      arabicText: map['arabic_text'] as String,
      transliteration: map['transliteration'] as String?,
      translation: map['translation'] as String?,
      count: map['count'] as int? ?? 1,
      orderIndex: map['order_index'] as int? ?? 0,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
      isSynced: (map['is_synced'] as int) == 1,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'routine_id': routineId,
      'arabic_text': arabicText,
      'transliteration': transliteration,
      'translation': translation,
      'count': count,
      'order_index': orderIndex,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_synced': isSynced ? 1 : 0,
    };
  }

  AthkarItem copyWith({
    String? id,
    String? routineId,
    String? arabicText,
    String? transliteration,
    String? translation,
    int? count,
    int? orderIndex,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSynced,
  }) {
    return AthkarItem(
      id: id ?? this.id,
      routineId: routineId ?? this.routineId,
      arabicText: arabicText ?? this.arabicText,
      transliteration: transliteration ?? this.transliteration,
      translation: translation ?? this.translation,
      count: count ?? this.count,
      orderIndex: orderIndex ?? this.orderIndex,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AthkarItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'AthkarItem(id: $id, routineId: $routineId, arabicText: $arabicText, '
           'transliteration: $transliteration, translation: $translation, '
           'count: $count, orderIndex: $orderIndex, createdAt: $createdAt, '
           'updatedAt: $updatedAt, isSynced: $isSynced)';
  }
}
