class AppConstants {
  // App Info
  static const String appName = 'ShadowSuite';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Production-ready cross-platform app with offline-first SQLite and Supabase sync';

  // Database
  static const String databaseName = 'shadowsuite.db';
  static const int databaseVersion = 1;

  // Supabase
  static const String supabaseUrl = 'YOUR_SUPABASE_URL';
  static const String supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';

  // Shared Preferences Keys
  static const String keyLanguage = 'language';
  static const String keyThemeMode = 'theme_mode';
  static const String keyFirstLaunch = 'first_launch';
  static const String keyUserId = 'user_id';
  static const String keyUserEmail = 'user_email';
  static const String keyLastSyncTime = 'last_sync_time';
  static const String keyOfflineMode = 'offline_mode';

  // Design Constants
  static const double defaultPadding = 16.0;
  static const double defaultMargin = 12.0;
  static const double borderRadius = 8.0;
  static const double cardElevation = 2.0;

  // Audio Recording
  static const int maxRecordingDuration = 3600; // 1 hour in seconds
  static const String audioFormat = 'aac';
  static const int audioSampleRate = 44100;
  static const int audioBitRate = 128000;

  // File Paths
  static const String recordingsPath = 'recordings';
  static const String transcriptionsPath = 'transcriptions';
  static const String backupsPath = 'backups';

  // Notification Channels
  static const String reminderChannelId = 'reminder_channel';
  static const String syncChannelId = 'sync_channel';
  static const String generalChannelId = 'general_channel';

  // Prayer Times
  static const List<String> prayerNames = [
    'fajr',
    'dhuhr',
    'asr',
    'maghrib',
    'isha',
  ];

  // Money Flow Categories
  static const List<String> defaultIncomeCategories = [
    'Salary',
    'Business',
    'Investment',
    'Gift',
    'Other Income',
  ];

  static const List<String> defaultExpenseCategories = [
    'Food & Dining',
    'Transportation',
    'Shopping',
    'Entertainment',
    'Bills & Utilities',
    'Healthcare',
    'Education',
    'Travel',
    'Other Expense',
  ];

  // Account Types
  static const List<String> accountTypes = [
    'Checking',
    'Savings',
    'Credit Card',
    'Cash',
    'Investment',
    'Loan',
  ];

  // Sync Settings
  static const int syncIntervalMinutes = 15;
  static const int maxRetryAttempts = 3;
  static const int connectionTimeoutSeconds = 30;

  // Validation
  static const int maxMemoTitleLength = 100;
  static const int maxMemoDescriptionLength = 1000;
  static const int maxAthkarTitleLength = 100;
  static const int maxTransactionDescriptionLength = 200;
  static const int maxCategoryNameLength = 50;
  static const int maxAccountNameLength = 50;

  // Date Formats
  static const String dateFormat = 'yyyy-MM-dd';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm:ss';
  static const String displayDateFormat = 'MMM dd, yyyy';
  static const String displayTimeFormat = 'h:mm a';

  // Error Messages
  static const String errorGeneral = 'An error occurred. Please try again.';
  static const String errorNetwork = 'Network error. Please check your connection.';
  static const String errorPermission = 'Permission denied. Please grant the required permissions.';
  static const String errorSync = 'Sync failed. Will retry automatically.';
  static const String errorDatabase = 'Database error. Please restart the app.';

  // Success Messages
  static const String successSave = 'Saved successfully';
  static const String successDelete = 'Deleted successfully';
  static const String successSync = 'Sync completed successfully';
  static const String successExport = 'Exported successfully';
  static const String successShare = 'Shared successfully';
}
