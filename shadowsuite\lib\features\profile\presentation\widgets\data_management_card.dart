import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/services/database_service.dart';
import '../../../../shared/widgets/custom_button.dart';

class DataManagementCard extends ConsumerStatefulWidget {
  final String userId;

  const DataManagementCard({
    super.key,
    required this.userId,
  });

  @override
  ConsumerState<DataManagementCard> createState() => _DataManagementCardState();
}

class _DataManagementCardState extends ConsumerState<DataManagementCard> {
  bool _isExporting = false;
  bool _isImporting = false;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Data Management',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            
            Text(
              'Export or import your data for backup and migration purposes.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: CustomButton(
                    text: 'Export Data',
                    icon: Icons.download,
                    type: ButtonType.outlined,
                    isLoading: _isExporting,
                    onPressed: _exportData,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: CustomButton(
                    text: 'Import Data',
                    icon: Icons.upload,
                    type: ButtonType.outlined,
                    isLoading: _isImporting,
                    onPressed: _importData,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            CustomButton(
              text: 'Clear All Data',
              icon: Icons.delete_forever,
              type: ButtonType.danger,
              onPressed: _showClearDataDialog,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _exportData() async {
    setState(() {
      _isExporting = true;
    });

    try {
      // Collect all user data
      final memoService = MemoDatabaseService();
      final athkarService = AthkarDatabaseService();
      final accountService = AccountDatabaseService();
      final categoryService = CategoryDatabaseService();
      final transactionService = TransactionDatabaseService();

      final memos = await memoService.getAll(userId: widget.userId);
      final athkarRoutines = await athkarService.getAll(userId: widget.userId);
      final accounts = await accountService.getAll(userId: widget.userId);
      final categories = await categoryService.getAll(userId: widget.userId);
      final transactions = await transactionService.getAll(userId: widget.userId);

      final exportData = {
        'export_date': DateTime.now().toIso8601String(),
        'user_id': widget.userId,
        'version': '1.0',
        'data': {
          'memos': memos.map((m) => m.toMap()).toList(),
          'athkar_routines': athkarRoutines.map((a) => a.toMap()).toList(),
          'accounts': accounts.map((a) => a.toMap()).toList(),
          'categories': categories.map((c) => c.toMap()).toList(),
          'transactions': transactions.map((t) => t.toMap()).toList(),
        },
      };

      // Save to file
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'shadowsuite_backup_${DateTime.now().millisecondsSinceEpoch}.json';
      final file = File('${directory.path}/$fileName');
      
      await file.writeAsString(
        const JsonEncoder.withIndent('  ').convert(exportData),
      );

      // Share the file
      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'ShadowSuite Data Export',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Data exported successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Export failed: $e')),
        );
      }
    } finally {
      setState(() {
        _isExporting = false;
      });
    }
  }

  Future<void> _importData() async {
    // This would typically open a file picker
    // For now, show a placeholder dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Import Data'),
        content: const Text(
          'Import functionality will be available in a future update. '
          'You can manually restore data by placing the backup file in the app directory.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showClearDataDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Data'),
        content: const Text(
          'This will permanently delete all your local data including memos, '
          'athkar routines, accounts, and transactions. This action cannot be undone.\n\n'
          'Are you sure you want to continue?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _clearAllData();
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Clear All Data'),
          ),
        ],
      ),
    );
  }

  Future<void> _clearAllData() async {
    try {
      final memoService = MemoDatabaseService();
      final athkarService = AthkarDatabaseService();
      final accountService = AccountDatabaseService();
      final categoryService = CategoryDatabaseService();
      final transactionService = TransactionDatabaseService();

      // Clear all data for the user
      final memos = await memoService.getAll(userId: widget.userId);
      for (final memo in memos) {
        await memoService.delete(memo.id);
      }

      final athkarRoutines = await athkarService.getAll(userId: widget.userId);
      for (final routine in athkarRoutines) {
        await athkarService.delete(routine.id);
      }

      final accounts = await accountService.getAll(userId: widget.userId);
      for (final account in accounts) {
        await accountService.delete(account.id);
      }

      final categories = await categoryService.getAll(userId: widget.userId);
      for (final category in categories) {
        await categoryService.delete(category.id);
      }

      final transactions = await transactionService.getAll(userId: widget.userId);
      for (final transaction in transactions) {
        await transactionService.delete(transaction.id);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('All data cleared successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to clear data: $e')),
        );
      }
    }
  }
}
